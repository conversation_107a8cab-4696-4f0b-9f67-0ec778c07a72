---
/**
 * Main Landing Page
 * Demonstrates mapping JSON data to components
 */

import Layout from '../layouts/Layout.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';

// Import all section components
import Hero from '../components/Hero.astro';
import Features from '../components/Features.astro';
import HowItWorks from '../components/HowItWorks.astro';
import SocialProof from '../components/SocialProof.astro';
import Screenshots from '../components/Screenshots.astro';
import CTA from '../components/CTA.astro';
import About from '../components/About.astro';
import FAQ from '../components/FAQ.astro';
import Footer from '../components/Footer.astro';
import Pricing from '../components/Pricing.astro';
import Newsletter from '../components/Newsletter.astro';

// Import JSON data using Astro's import
import userConfigData from '../data/userConfig.json';
import landingPageData from '../data/landingPage.json';

import type { SupportedLocale } from '../lib/localization';

// Get locale from URL parameters or default to 'en'
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';

// Get data source from URL parameter (demo feature)
const dataSource = url.searchParams.get('data') || 'user';
const landingPage = dataSource === 'template' ? landingPageData.landingPage : userConfigData.landingPage;

// Extract meta information for SEO
const metaTitle = landingPage.meta?.[locale]?.title || landingPage.meta?.en?.title || 'Mobile App Landing Page';
const metaDescription = landingPage.meta?.[locale]?.description || landingPage.meta?.en?.description || 'Amazing mobile app for productivity';
const metaKeywords = landingPage.meta?.[locale]?.keywords || landingPage.meta?.en?.keywords || 'mobile app, productivity, ios, android';
---

<Layout title={metaTitle}>
  <!-- SEO Meta Tags -->
  <meta name="description" content={metaDescription} />
  <meta name="keywords" content={metaKeywords} />
  <meta name="author" content="Your App Team" />

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content={metaTitle} />
  <meta property="og:description" content={metaDescription} />
  <meta property="og:type" content="website" />
  <meta property="og:url" content={Astro.url} />
  {landingPage.hero?.appLogo && (
    <meta property="og:image" content={landingPage.hero.appLogo} />
  )}

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content={metaTitle} />
  <meta name="twitter:description" content={metaDescription} />
  {landingPage.hero?.appLogo && (
    <meta name="twitter:image" content={landingPage.hero.appLogo} />
  )}

  <!-- Language Switcher (Fixed Position) -->
  <div class="fixed top-4 right-4 z-50">
    <LanguageSwitcher currentLocale={locale} variant="outline" size="sm" />
  </div>

  <!-- Main Content -->
  <main>

    <!-- Hero Section -->
    <Hero
      data={landingPage.hero}
      locale={locale}
    />

    <!-- Features Section -->
    <Features
      data={landingPage.features}
      locale={locale}
    />

    <!-- How It Works Section -->
    <HowItWorks
      data={landingPage.howItWorks}
      locale={locale}
    />

    <!-- Social Proof Section -->
    <SocialProof
      data={landingPage.socialProof}
      locale={locale}
    />

    <!-- Screenshots Section -->
    <Screenshots
      data={landingPage.screenshots}
      locale={locale}
    />

    <!-- CTA Section -->
    <CTA
      data={landingPage.cta}
      locale={locale}
    />

    <!-- About Section -->
    <About
      data={landingPage.about}
      locale={locale}
    />

    <!-- FAQ Section -->
    <FAQ
      data={landingPage.faq}
      locale={locale}
    />

    <!-- Optional Pricing Section -->
    {landingPage.optional?.pricing?.enabled && (
      <Pricing
        data={landingPage.optional.pricing}
        locale={locale}
      />
    )}

    <!-- Optional Newsletter Section -->
    {landingPage.optional?.newsletter?.enabled && (
      <Newsletter
        data={landingPage.optional.newsletter}
        locale={locale}
      />
    )}

    <!-- Footer Section -->
    <Footer
      data={landingPage.footer}
      locale={locale}
    />

  </main>
</Layout>
