---
/**
 * Test page for localization functionality
 * This page demonstrates how the localization system works
 */

import Layout from '../layouts/Layout.astro';
import LocalizationExample from '../components/LocalizationExample.astro';
import type { SupportedLocale } from '../lib/localization';

// Get locale from URL parameters or default to 'en'
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';
---

<Layout title="Localization Test">
  <main>
    <LocalizationExample locale={locale} />
  </main>
</Layout>
