---
/**
 * Test page for localization functionality
 * This page demonstrates how the localization system works
 */

import Layout from '../layouts/Layout.astro';
import LocalizationExample from '../components/LocalizationExample.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
import LanguageSwitcherDropdown from '../components/LanguageSwitcherDropdown.astro';
import type { SupportedLocale } from '../lib/localization';

// Get locale from URL parameters or default to 'en'
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';
---

<Layout title="Localization Test">
  <main>
    <!-- Language Switcher Demo -->
    <div class="bg-white border-b border-gray-200 p-4">
      <div class="max-w-4xl mx-auto flex items-center justify-between">
        <h1 class="text-2xl font-bold">Localization Test</h1>
        <div class="flex items-center gap-4">
          <div class="flex flex-col items-end gap-2">
            <span class="text-sm text-gray-600">Button Style:</span>
            <LanguageSwitcher currentLocale={locale} />
          </div>
          <div class="flex flex-col items-end gap-2">
            <span class="text-sm text-gray-600">Dropdown Style:</span>
            <LanguageSwitcherDropdown currentLocale={locale} />
          </div>
        </div>
      </div>
    </div>

    <LocalizationExample locale={locale} />
  </main>
</Layout>
