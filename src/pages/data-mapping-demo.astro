---
/**
 * Data Mapping Demonstration Page
 * Shows different ways to map JSON data to components
 */

import Layout from '../layouts/Layout.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';

// Import section components
import Hero from '../components/Hero.astro';
import Features from '../components/Features.astro';
import HowItWorks from '../components/HowItWorks.astro';
import SocialProof from '../components/SocialProof.astro';

// Import JSON data using Astro's import
import userConfigData from '../data/userConfig.json';
import landingPageData from '../data/landingPage.json';

import type { SupportedLocale } from '../lib/localization';

// Get locale from URL parameters
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';

// Example 1: Direct data mapping from userConfig
const userLandingPage = userConfigData.landingPage;

// Example 2: Direct data mapping from template
const templateLandingPage = landingPageData.landingPage;

// Example 3: Selective data mapping (mixing sources)
const mixedData = {
  hero: userConfigData.landingPage.hero,
  features: landingPageData.landingPage.features, // Use template features
  howItWorks: userConfigData.landingPage.howItWorks,
  socialProof: templateLandingPage.socialProof // Use template social proof
};

// Example 4: Data transformation before passing to components
const transformedHeroData = {
  ...userLandingPage.hero,
  // Add custom properties or modify existing ones
  customClass: 'demo-hero',
  // Override CTA buttons with custom styling
  ctaButtons: userLandingPage.hero.ctaButtons?.map((button: any) => ({
    ...button,
    variant: 'secondary' // Override variant
  }))
};

// Example 5: Conditional data selection
const selectedDataSource = url.searchParams.get('source') || 'user';
const dynamicLandingPage = selectedDataSource === 'template' ? templateLandingPage : userLandingPage;
---

<Layout title="Data Mapping Demo">
  <!-- Language Switcher -->
  <div class="fixed top-4 right-4 z-50">
    <LanguageSwitcher currentLocale={locale} variant="outline" size="sm" />
  </div>
  
  <!-- Data Source Switcher -->
  <div class="fixed top-4 left-4 z-50">
    <div class="bg-white rounded-lg shadow-lg p-4">
      <p class="text-sm font-medium text-gray-700 mb-2">Data Source:</p>
      <div class="flex gap-2">
        <a 
          href="?source=user" 
          class={`px-3 py-1 rounded text-sm ${selectedDataSource === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          User Config
        </a>
        <a 
          href="?source=template" 
          class={`px-3 py-1 rounded text-sm ${selectedDataSource === 'template' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
        >
          Template
        </a>
      </div>
    </div>
  </div>

  <main class="pt-20">
    
    <!-- Demo Section Headers -->
    <div class="bg-gray-100 py-8">
      <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold text-center mb-4">Data Mapping Demonstration</h1>
        <p class="text-center text-gray-600 max-w-2xl mx-auto">
          This page demonstrates different ways to map JSON data to Astro components. 
          Switch between data sources and languages to see how components adapt.
        </p>
      </div>
    </div>

    <!-- Example 1: Direct User Config Data Mapping -->
    <section class="py-8 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold mb-2">Example 1: Direct User Config Mapping</h2>
          <p class="text-gray-600">
            <code class="bg-gray-100 px-2 py-1 rounded">{'<Hero data={userConfigData.landingPage.hero} locale={locale} />'}</code>
          </p>
        </div>
      </div>
      
      <Hero 
        data={userLandingPage.hero} 
        locale={locale}
        className="min-h-[60vh]"
      />
    </section>

    <!-- Example 2: Template Data Mapping -->
    <section class="py-8 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold mb-2">Example 2: Template Data Mapping</h2>
          <p class="text-gray-600">
            <code class="bg-gray-100 px-2 py-1 rounded">{'<Features data={landingPageData.landingPage.features} locale={locale} />'}</code>
          </p>
        </div>
      </div>
      
      <Features 
        data={templateLandingPage.features} 
        locale={locale}
      />
    </section>

    <!-- Example 3: Mixed Data Sources -->
    <section class="py-8 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold mb-2">Example 3: Mixed Data Sources</h2>
          <p class="text-gray-600">
            <code class="bg-gray-100 px-2 py-1 rounded">{'<HowItWorks data={mixedData.howItWorks} locale={locale} />'}</code>
          </p>
        </div>
      </div>
      
      <HowItWorks 
        data={mixedData.howItWorks} 
        locale={locale}
      />
    </section>

    <!-- Example 4: Transformed Data -->
    <section class="py-8 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold mb-2">Example 4: Transformed Data</h2>
          <p class="text-gray-600 mb-4">
            Data transformed before passing to component (CTA buttons changed to secondary variant)
          </p>
          <details class="text-left max-w-2xl mx-auto">
            <summary class="cursor-pointer text-blue-600 hover:text-blue-800">View transformation code</summary>
            <pre class="bg-gray-100 p-4 rounded mt-2 text-sm overflow-x-auto"><code>{`const transformedHeroData = {
  ...userLandingPage.hero,
  customClass: 'demo-hero',
  ctaButtons: userLandingPage.hero.ctaButtons?.map(button => ({
    ...button,
    variant: 'secondary' // Override variant
  }))
};`}</code></pre>
          </details>
        </div>
      </div>
      
      <Hero 
        data={transformedHeroData} 
        locale={locale}
        className="min-h-[60vh] demo-hero"
      />
    </section>

    <!-- Example 5: Dynamic Data Selection -->
    <section class="py-8 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-bold mb-2">Example 5: Dynamic Data Selection</h2>
          <p class="text-gray-600 mb-4">
            Data source selected based on URL parameter: <strong>{selectedDataSource}</strong>
          </p>
          <p class="text-gray-600">
            <code class="bg-gray-100 px-2 py-1 rounded">{'<SocialProof data={dynamicLandingPage.socialProof} locale={locale} />'}</code>
          </p>
        </div>
      </div>
      
      <SocialProof 
        data={dynamicLandingPage.socialProof} 
        locale={locale}
      />
    </section>

    <!-- Code Examples Section -->
    <section class="py-16 bg-gray-900 text-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Data Mapping Patterns</h2>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          
          <!-- Pattern 1: Basic Import and Usage -->
          <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-blue-400">1. Basic Import and Usage</h3>
            <pre class="text-sm overflow-x-auto"><code>{`// Import JSON data
import userData from '../data/userConfig.json';

// Use in component
<Hero 
  data={userData.landingPage.hero} 
  locale="en" 
/>`}</code></pre>
          </div>

          <!-- Pattern 2: Dynamic Locale -->
          <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-green-400">2. Dynamic Locale</h3>
            <pre class="text-sm overflow-x-auto"><code>{`// Get locale from URL
const url = new URL(Astro.request.url);
const locale = url.searchParams.get('locale') || 'en';

// Pass to component
<Features 
  data={landingPage.features} 
  locale={locale} 
/>`}</code></pre>
          </div>

          <!-- Pattern 3: Conditional Rendering -->
          <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-purple-400">3. Conditional Rendering</h3>
            <pre class="text-sm overflow-x-auto"><code>{`// Conditional optional sections
{landingPage.optional?.pricing?.enabled && (
  <Pricing 
    data={landingPage.optional.pricing} 
    locale={locale} 
  />
)}`}</code></pre>
          </div>

          <!-- Pattern 4: Data Transformation -->
          <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-yellow-400">4. Data Transformation</h3>
            <pre class="text-sm overflow-x-auto"><code>{`// Transform data before passing
const enhancedData = {
  ...originalData,
  customProperty: 'value',
  items: originalData.items?.map(item => ({
    ...item,
    enhanced: true
  }))
};`}</code></pre>
          </div>
          
        </div>
      </div>
    </section>

  </main>
</Layout>

<style>
  /* Custom styling for demo hero */
  .demo-hero {
    position: relative;
  }
  
  .demo-hero::before {
    content: 'TRANSFORMED DATA';
    position: absolute;
    top: 4rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.9);
    color: #1f2937;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
    z-index: 10;
  }
  
  /* Code block styling */
  pre code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.5;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .demo-hero::before {
      top: 2rem;
      right: 1rem;
      font-size: 0.625rem;
      padding: 0.25rem 0.5rem;
    }
  }
</style>
