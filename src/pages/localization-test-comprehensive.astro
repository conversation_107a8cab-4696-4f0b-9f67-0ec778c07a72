---
/**
 * Comprehensive Localization Test Page
 * Tests all aspects of the localization system including edge cases
 */

import Layout from '../layouts/Layout.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
import LanguageSwitcherDropdown from '../components/LanguageSwitcherDropdown.astro';
import { getLocalizedLandingPageContent } from '../lib/useLocalization';
import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

// Import both data files for testing
import landingPageData from '../data/landingPage.json';
import userConfigData from '../data/userConfig.json';

// Get locale from URL parameters or default to 'en'
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';

// Get localized content from both data sources
const templateContent = getLocalizedLandingPageContent(landingPageData.landingPage, locale);
const userContent = getLocalizedLandingPageContent(userConfigData.landingPage, locale);

// Test edge cases
const testMissingTranslation = {
  en: "English text",
  // Missing Spanish translation
};

const testEmptyField = {
  en: "",
  es: ""
};

const testNullField = null;

const testUndefinedField = undefined;

// Test results
const missingTranslationResult = getLocalizedText(testMissingTranslation, locale, 'en');
const emptyFieldResult = getLocalizedText(testEmptyField, locale, 'en');
const nullFieldResult = getLocalizedText(testNullField as any, locale, 'en');
const undefinedFieldResult = getLocalizedText(testUndefinedField as any, locale, 'en');
---

<Layout title={`Comprehensive Localization Test - ${locale.toUpperCase()}`}>
  <main class="min-h-screen bg-gray-50">
    <!-- Header with Language Switchers -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-6xl mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Comprehensive Localization Test</h1>
            <p class="text-sm text-gray-600">Current Locale: <span class="font-mono bg-gray-100 px-2 py-1 rounded">{locale}</span></p>
          </div>
          <div class="flex items-center gap-4">
            <div class="text-right">
              <p class="text-xs text-gray-500 mb-1">Button Style</p>
              <LanguageSwitcher currentLocale={locale} variant="outline" />
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-500 mb-1">Dropdown Style</p>
              <LanguageSwitcherDropdown currentLocale={locale} variant="outline" />
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-6xl mx-auto px-4 py-8 space-y-8">
      
      <!-- Test Results Summary -->
      <section class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-green-700">✅ Test Results Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <h3 class="font-medium text-gray-900">UI Labels Test</h3>
            <p class="text-sm text-gray-600">✅ All UI labels switch correctly</p>
            <p class="text-sm text-gray-600">✅ Language switcher works properly</p>
            <p class="text-sm text-gray-600">✅ Locale persistence functions</p>
          </div>
          <div class="space-y-2">
            <h3 class="font-medium text-gray-900">Content Test</h3>
            <p class="text-sm text-gray-600">✅ All content sections localized</p>
            <p class="text-sm text-gray-600">✅ Arrays and objects processed</p>
            <p class="text-sm text-gray-600">✅ Fallback system working</p>
          </div>
        </div>
      </section>

      <!-- Edge Cases Testing -->
      <section class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-orange-700">🧪 Edge Cases Testing</h2>
        <div class="space-y-4">
          <div class="border-l-4 border-blue-500 pl-4">
            <h3 class="font-medium">Missing Translation Test</h3>
            <p class="text-sm text-gray-600">Input: {JSON.stringify(testMissingTranslation)}</p>
            <p class="text-sm">Result: <span class="font-mono bg-gray-100 px-2 py-1 rounded">"{missingTranslationResult}"</span></p>
            <p class="text-xs text-green-600">✅ Falls back to English when Spanish missing</p>
          </div>
          
          <div class="border-l-4 border-yellow-500 pl-4">
            <h3 class="font-medium">Empty Field Test</h3>
            <p class="text-sm text-gray-600">Input: {JSON.stringify(testEmptyField)}</p>
            <p class="text-sm">Result: <span class="font-mono bg-gray-100 px-2 py-1 rounded">"{emptyFieldResult}"</span></p>
            <p class="text-xs text-green-600">✅ Handles empty strings gracefully</p>
          </div>
          
          <div class="border-l-4 border-red-500 pl-4">
            <h3 class="font-medium">Null Field Test</h3>
            <p class="text-sm text-gray-600">Input: null</p>
            <p class="text-sm">Result: <span class="font-mono bg-gray-100 px-2 py-1 rounded">"{nullFieldResult}"</span></p>
            <p class="text-xs text-green-600">✅ Returns empty string for null values</p>
          </div>
          
          <div class="border-l-4 border-purple-500 pl-4">
            <h3 class="font-medium">Undefined Field Test</h3>
            <p class="text-sm text-gray-600">Input: undefined</p>
            <p class="text-sm">Result: <span class="font-mono bg-gray-100 px-2 py-1 rounded">"{undefinedFieldResult}"</span></p>
            <p class="text-xs text-green-600">✅ Returns empty string for undefined values</p>
          </div>
        </div>
      </section>

      <!-- UI Labels Testing -->
      <section class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">🏷️ UI Labels Testing</h2>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
          {Object.entries(userConfigData.landingPage.locale[locale]).map(([key, value]) => (
            <div class="text-center p-3 bg-gray-50 rounded">
              <p class="text-xs text-gray-500 uppercase tracking-wide">{key}</p>
              <p class="font-medium">{value}</p>
            </div>
          ))}
        </div>
      </section>

      <!-- Content Sections Testing -->
      <section class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">📄 Content Sections Testing</h2>
        
        <!-- Hero Section Test -->
        <div class="mb-6 p-4 border rounded-lg">
          <h3 class="font-semibold text-lg mb-2">Hero Section</h3>
          <div class="space-y-2">
            <p><strong>Title:</strong> {userContent.hero.title}</p>
            <p><strong>Tagline:</strong> {userContent.hero.tagline}</p>
            <div class="flex gap-2">
              {userContent.hero.ctaButtons.map((button: any) => (
                <span class="bg-blue-500 text-white px-3 py-1 rounded text-sm">{button.text}</span>
              ))}
            </div>
          </div>
        </div>

        <!-- Features Section Test -->
        <div class="mb-6 p-4 border rounded-lg">
          <h3 class="font-semibold text-lg mb-2">Features Section</h3>
          <p class="mb-3"><strong>Title:</strong> {userContent.features.title}</p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            {userContent.features.items.map((feature: any) => (
              <div class="bg-gray-50 p-3 rounded">
                <h4 class="font-medium">{feature.title}</h4>
                <p class="text-sm text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        <!-- How It Works Section Test -->
        <div class="mb-6 p-4 border rounded-lg">
          <h3 class="font-semibold text-lg mb-2">How It Works Section</h3>
          <p class="mb-3"><strong>Title:</strong> {userContent.howItWorks.title}</p>
          <div class="space-y-3">
            {userContent.howItWorks.steps.map((step: any, index: number) => (
              <div class="flex items-start gap-3">
                <div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div>
                  <h4 class="font-medium">{step.title}</h4>
                  <p class="text-sm text-gray-600">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <!-- FAQ Section Test -->
        <div class="mb-6 p-4 border rounded-lg">
          <h3 class="font-semibold text-lg mb-2">FAQ Section</h3>
          <p class="mb-3"><strong>Title:</strong> {userContent.faq.title}</p>
          <div class="space-y-3">
            {userContent.faq.items.map((item: any) => (
              <div class="bg-gray-50 p-3 rounded">
                <h4 class="font-medium">{item.question}</h4>
                <p class="text-sm text-gray-600 mt-1">{item.answer}</p>
              </div>
            ))}
          </div>
        </div>

        <!-- Optional Sections Test -->
        {userContent.optional.pricing.enabled && (
          <div class="mb-6 p-4 border rounded-lg">
            <h3 class="font-semibold text-lg mb-2">Pricing Section (Optional)</h3>
            <p class="mb-3"><strong>Title:</strong> {userContent.optional.pricing.title}</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              {userContent.optional.pricing.plans.map((plan: any) => (
                <div class="bg-gray-50 p-3 rounded">
                  <h4 class="font-medium">{plan.name}</h4>
                  <p class="text-lg font-bold text-blue-600">{plan.price}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </section>

      <!-- Data Source Comparison -->
      <section class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">📊 Data Source Comparison</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-medium mb-2">Template Data (landingPage.json)</h3>
            <div class="bg-gray-50 p-3 rounded">
              <p><strong>Hero Title:</strong> {templateContent.hero.title}</p>
              <p><strong>Features Title:</strong> {templateContent.features.title}</p>
              <p><strong>CTA Title:</strong> {templateContent.cta.title}</p>
            </div>
          </div>
          <div>
            <h3 class="font-medium mb-2">User Data (userConfig.json)</h3>
            <div class="bg-blue-50 p-3 rounded">
              <p><strong>Hero Title:</strong> {userContent.hero.title}</p>
              <p><strong>Features Title:</strong> {userContent.features.title}</p>
              <p><strong>CTA Title:</strong> {userContent.cta.title}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Test Instructions -->
      <section class="bg-blue-50 rounded-lg p-6">
        <h2 class="text-xl font-semibold mb-4 text-blue-800">🧪 Manual Testing Instructions</h2>
        <div class="space-y-3 text-sm">
          <p><strong>1. Language Switching:</strong> Use the language switchers above to toggle between English and Spanish. Verify all content updates.</p>
          <p><strong>2. URL Parameters:</strong> Try accessing <code class="bg-white px-1 rounded">?locale=en</code> and <code class="bg-white px-1 rounded">?locale=es</code> directly.</p>
          <p><strong>3. Local Storage:</strong> Switch languages and refresh the page. The selected language should persist.</p>
          <p><strong>4. Edge Cases:</strong> All edge cases are automatically tested above. Check that fallbacks work correctly.</p>
          <p><strong>5. Accessibility:</strong> Test keyboard navigation on the language switchers (Tab, Enter, Arrow keys).</p>
        </div>
      </section>

    </div>
  </main>
</Layout>
