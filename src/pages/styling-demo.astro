---
/**
 * Styling Demonstration Page
 * Showcases enhanced styling system with shadcn variants and accessibility features
 */

import Layout from '../layouts/Layout.astro';
import Button from '../components/ui/Button.astro';
import Card from '../components/ui/Card.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';

// Import styling utilities
import { getButtonClasses, getCardClasses, cn } from '../styles/component-variants';
---

<Layout 
  title="Styling System Demo"
  description="Comprehensive demonstration of the enhanced styling system with shadcn variants and accessibility features"
>
  <!-- Language Switcher -->
  <div class="fixed top-4 right-4 z-50">
    <LanguageSwitcher currentLocale="en" variant="outline" size="sm" />
  </div>

  <main class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
    
    <!-- Hero Section -->
    <section class="py-20 text-center">
      <div class="container mx-auto px-4">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 gradient-text">
          Enhanced Styling System
        </h1>
        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Comprehensive styling system with shadcn design principles, mobile-first responsive design, and full accessibility support
        </p>
        <div class="flex flex-wrap gap-4 justify-center">
          <Button variant="primary" size="lg">Get Started</Button>
          <Button variant="outline" size="lg">Learn More</Button>
        </div>
      </div>
    </section>

    <!-- Button Variants Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Button Variants</h2>
        
        <!-- Button Variants Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          
          <!-- Default Variants -->
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-4">Default Variants</h3>
            <div class="space-y-3">
              <Button variant="default" className="w-full">Default</Button>
              <Button variant="primary" className="w-full">Primary</Button>
              <Button variant="secondary" className="w-full">Secondary</Button>
              <Button variant="outline" className="w-full">Outline</Button>
            </div>
          </div>
          
          <!-- Special Variants -->
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-4">Special Variants</h3>
            <div class="space-y-3">
              <Button variant="cta" className="w-full">CTA Button</Button>
              <Button variant="destructive" className="w-full">Destructive</Button>
              <Button variant="ghost" className="w-full">Ghost</Button>
              <Button variant="link" className="w-full">Link</Button>
            </div>
          </div>
          
          <!-- Button Sizes -->
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-4">Button Sizes</h3>
            <div class="space-y-3">
              <Button variant="primary" size="sm" className="w-full">Small</Button>
              <Button variant="primary" size="default" className="w-full">Default</Button>
              <Button variant="primary" size="lg" className="w-full">Large</Button>
              <Button variant="primary" size="xl" className="w-full">Extra Large</Button>
            </div>
          </div>
          
          <!-- Button States -->
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-4">Button States</h3>
            <div class="space-y-3">
              <Button variant="primary" className="w-full">Normal</Button>
              <Button variant="primary" loading={true} className="w-full">Loading</Button>
              <Button variant="primary" disabled={true} className="w-full">Disabled</Button>
              <Button variant="primary" size="icon" ariaLabel="Icon button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
              </Button>
            </div>
          </div>
          
        </div>
        
        <!-- Interactive Button Demo -->
        <div class="text-center">
          <h3 class="text-xl font-semibold mb-6">Interactive Features</h3>
          <div class="flex flex-wrap gap-4 justify-center">
            <Button variant="primary" href="#demo" target="_blank">External Link</Button>
            <Button variant="outline" type="submit">Submit Form</Button>
            <Button variant="cta" className="animate-pulse">Animated CTA</Button>
          </div>
        </div>
      </div>
    </section>

    <!-- Card Variants Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Card Variants</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          <!-- Default Card -->
          <Card variant="default" padding="default">
            <h3 class="text-xl font-semibold mb-3">Default Card</h3>
            <p class="text-gray-600 mb-4">Basic card with default styling and padding.</p>
            <Button variant="outline" size="sm">Learn More</Button>
          </Card>
          
          <!-- Elevated Card -->
          <Card variant="elevated" padding="lg">
            <h3 class="text-xl font-semibold mb-3">Elevated Card</h3>
            <p class="text-gray-600 mb-4">Enhanced shadow and hover effects for better visual hierarchy.</p>
            <Button variant="primary" size="sm">Get Started</Button>
          </Card>
          
          <!-- Interactive Card -->
          <Card variant="interactive" padding="default" clickable={true}>
            <h3 class="text-xl font-semibold mb-3">Interactive Card</h3>
            <p class="text-gray-600 mb-4">Clickable card with hover animations and keyboard navigation.</p>
            <div class="text-blue-600 text-sm font-medium">Click me →</div>
          </Card>
          
          <!-- Feature Card -->
          <Card variant="feature" padding="lg">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">Feature Card</h3>
            <p class="text-gray-600">Specialized styling for feature showcases with icon support.</p>
          </Card>
          
          <!-- Testimonial Card -->
          <Card variant="testimonial" padding="lg">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
              <div>
                <div class="font-semibold">John Doe</div>
                <div class="text-sm text-gray-500">Customer</div>
              </div>
            </div>
            <p class="text-gray-600 italic">"This styling system is amazing! The accessibility features and responsive design make it perfect for our needs."</p>
          </Card>
          
          <!-- Link Card -->
          <Card variant="elevated" padding="default" href="#external" target="_blank">
            <h3 class="text-xl font-semibold mb-3">Link Card</h3>
            <p class="text-gray-600 mb-4">Card that functions as a link with proper accessibility attributes.</p>
            <div class="text-blue-600 text-sm font-medium">Visit external site →</div>
          </Card>
          
        </div>
      </div>
    </section>

    <!-- Responsive Design Demo -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Responsive Design</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div class="bg-blue-100 p-6 rounded-lg text-center">
            <div class="text-2xl font-bold text-blue-600 mb-2">Mobile First</div>
            <p class="text-sm text-blue-800">Designed for mobile devices with progressive enhancement</p>
          </div>
          <div class="bg-green-100 p-6 rounded-lg text-center">
            <div class="text-2xl font-bold text-green-600 mb-2">Flexible Grid</div>
            <p class="text-sm text-green-800">Responsive grid system that adapts to all screen sizes</p>
          </div>
          <div class="bg-purple-100 p-6 rounded-lg text-center">
            <div class="text-2xl font-bold text-purple-600 mb-2">Touch Friendly</div>
            <p class="text-sm text-purple-800">Optimized touch targets for mobile interaction</p>
          </div>
          <div class="bg-orange-100 p-6 rounded-lg text-center">
            <div class="text-2xl font-bold text-orange-600 mb-2">Performance</div>
            <p class="text-sm text-orange-800">Optimized CSS for fast loading and smooth animations</p>
          </div>
        </div>
        
        <!-- Breakpoint Demo -->
        <div class="bg-gray-100 p-6 rounded-lg">
          <h3 class="text-xl font-semibold mb-4">Responsive Breakpoints</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 text-center">
            <div class="bg-white p-4 rounded">
              <div class="font-semibold">Mobile</div>
              <div class="text-sm text-gray-600">&lt; 640px</div>
            </div>
            <div class="bg-white p-4 rounded">
              <div class="font-semibold">SM</div>
              <div class="text-sm text-gray-600">≥ 640px</div>
            </div>
            <div class="bg-white p-4 rounded">
              <div class="font-semibold">MD</div>
              <div class="text-sm text-gray-600">≥ 768px</div>
            </div>
            <div class="bg-white p-4 rounded">
              <div class="font-semibold">LG</div>
              <div class="text-sm text-gray-600">≥ 1024px</div>
            </div>
            <div class="bg-white p-4 rounded">
              <div class="font-semibold">XL</div>
              <div class="text-sm text-gray-600">≥ 1280px</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Accessibility Features -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Accessibility Features</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          <!-- Keyboard Navigation -->
          <div class="bg-white p-6 rounded-lg">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">Keyboard Navigation</h3>
            <p class="text-gray-600 mb-4">Full keyboard support with visible focus indicators and logical tab order.</p>
            <Button variant="outline" size="sm" className="focus-ring">Try Tab Navigation</Button>
          </div>
          
          <!-- Screen Reader Support -->
          <div class="bg-white p-6 rounded-lg">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">Screen Reader Support</h3>
            <p class="text-gray-600 mb-4">Proper ARIA labels, semantic HTML, and descriptive text for assistive technologies.</p>
            <Button variant="outline" size="sm" ariaLabel="Example button with ARIA label">ARIA Example</Button>
          </div>
          
          <!-- High Contrast Mode -->
          <div class="bg-white p-6 rounded-lg">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">High Contrast Mode</h3>
            <p class="text-gray-600 mb-4">Automatic adaptation to high contrast preferences with enhanced borders and colors.</p>
            <div class="text-sm text-gray-500">Enable high contrast in your OS to see the effect</div>
          </div>
          
        </div>
      </div>
    </section>

    <!-- Animation Demo -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center mb-12">Animation System</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="text-center p-6 bg-gray-50 rounded-lg animate-fade-in animate-delay-100">
            <div class="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-4 animate-pulse"></div>
            <h3 class="font-semibold">Fade In</h3>
            <p class="text-sm text-gray-600">Smooth fade in animation</p>
          </div>
          
          <div class="text-center p-6 bg-gray-50 rounded-lg animate-slide-up animate-delay-200">
            <div class="w-16 h-16 bg-green-500 rounded-full mx-auto mb-4 animate-bounce"></div>
            <h3 class="font-semibold">Slide Up</h3>
            <p class="text-sm text-gray-600">Slide up from bottom</p>
          </div>
          
          <div class="text-center p-6 bg-gray-50 rounded-lg animate-scale-in animate-delay-300">
            <div class="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-4 animate-spin"></div>
            <h3 class="font-semibold">Scale In</h3>
            <p class="text-sm text-gray-600">Scale in animation</p>
          </div>
          
          <div class="text-center p-6 bg-gray-50 rounded-lg animate-slide-in-right animate-delay-400">
            <div class="w-16 h-16 bg-orange-500 rounded-full mx-auto mb-4 animate-float"></div>
            <h3 class="font-semibold">Float</h3>
            <p class="text-sm text-gray-600">Floating animation</p>
          </div>
        </div>
        
        <div class="text-center mt-8">
          <p class="text-gray-600 mb-4">All animations respect user's motion preferences</p>
          <div class="text-sm text-gray-500">Animations are disabled for users who prefer reduced motion</div>
        </div>
      </div>
    </section>

  </main>
</Layout>

<style>
  /* Additional demo-specific styles */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Demo animation classes */
  .animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }
  
  .animate-slide-up {
    opacity: 0;
    transform: translateY(2rem);
    animation: slideUp 0.6s ease-out forwards;
  }
  
  .animate-scale-in {
    opacity: 0;
    transform: scale(0.95);
    animation: scaleIn 0.6s ease-out forwards;
  }
  
  .animate-slide-in-right {
    opacity: 0;
    transform: translateX(2rem);
    animation: slideInRight 0.6s ease-out forwards;
  }
  
  @keyframes fadeIn {
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes scaleIn {
    to { opacity: 1; transform: scale(1); }
  }
  
  @keyframes slideInRight {
    to { opacity: 1; transform: translateX(0); }
  }
</style>
