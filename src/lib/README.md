# Localization System

This directory contains the localization utilities for the mobile app landing page.

## Overview

The localization system provides utilities to extract and render localized content from JSON data structures. It supports mapping fields like `hero.title.en` and `hero.title.es` to render content based on the selected language.

## Files

- `localization.ts` - Core localization utilities
- `useLocalization.ts` - High-level localization helper and convenience functions
- `README.md` - This documentation file

## Usage

### Basic Text Localization

```typescript
import { getLocalizedText } from './localization';

const content = {
  en: "Hello World",
  es: "Hola Mundo"
};

const englishText = getLocalizedText(content, 'en'); // "Hello World"
const spanishText = getLocalizedText(content, 'es'); // "Hola Mundo"
```

### Using the Localization Helper

```typescript
import { createLocalizationHelper } from './useLocalization';

const helper = createLocalizationHelper('es'); // Spanish locale

// Get localized text
const title = helper.t({ en: "Features", es: "Características" }); // "Características"

// Get field from nested object
const heroTitle = helper.getField(landingPageData, 'hero.title'); // Gets hero.title.es

// Localize entire object
const localizedHero = helper.localizeObject(landingPageData.hero);
```

### Complete Landing Page Localization

```typescript
import { getLocalizedLandingPageContent } from './useLocalization';
import landingPageData from '../data/userConfig.json';

const content = getLocalizedLandingPageContent(landingPageData.landingPage, 'es');

// Now all content is localized to Spanish
console.log(content.hero.title); // Spanish title
console.log(content.features.items); // Array of features with Spanish text
```

### In Astro Components

```astro
---
import { getLocalizedLandingPageContent } from '../lib/useLocalization';
import landingPageData from '../data/userConfig.json';

const locale = 'en'; // Get from astro-i18next or URL params
const content = getLocalizedLandingPageContent(landingPageData.landingPage, locale);
---

<h1>{content.hero.title}</h1>
<p>{content.hero.tagline}</p>

{content.features.items.map(feature => (
  <div>
    <h3>{feature.title}</h3>
    <p>{feature.description}</p>
  </div>
))}
```

## Supported Locales

Currently supported locales:
- `en` - English (default)
- `es` - Spanish

## JSON Structure

The localization system expects JSON content to follow this structure:

```json
{
  "hero": {
    "title": {
      "en": "App Name",
      "es": "Nombre de la App"
    },
    "tagline": {
      "en": "Amazing tagline",
      "es": "Eslogan increíble"
    }
  },
  "features": {
    "items": [
      {
        "title": {
          "en": "Feature 1",
          "es": "Característica 1"
        },
        "description": {
          "en": "Feature description",
          "es": "Descripción de la característica"
        }
      }
    ]
  }
}
```

## Functions Reference

### Core Functions (`localization.ts`)

- `getLocalizedText(content, locale, fallbackLocale)` - Extract localized text
- `getLocalizedArray(items, locale, fallbackLocale)` - Localize array items
- `localizeObject(obj, locale, fallbackLocale)` - Deep localize an object
- `isLocalizedContent(value)` - Check if value is localized content

### Helper Functions (`useLocalization.ts`)

- `createLocalizationHelper(locale, fallbackLocale)` - Create helper instance
- `getLocalizedLandingPageContent(data, locale)` - Get complete localized landing page
- `LocalizationHelper.t(content)` - Get localized text
- `LocalizationHelper.getField(data, path)` - Get field by path
- `LocalizationHelper.localizeObject(obj)` - Localize object
- `LocalizationHelper.localizeArray(items)` - Localize array

## Testing

Visit `/localization-test` to see the localization system in action. You can switch between languages using URL parameters:

- `/localization-test?locale=en` - English
- `/localization-test?locale=es` - Spanish

## Integration with astro-i18next

The system is designed to work with astro-i18next for complete internationalization. The locale can be obtained from astro-i18next and passed to the localization functions.
