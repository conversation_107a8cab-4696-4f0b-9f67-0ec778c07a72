/**
 * Localization utilities for extracting localized content from JSON data
 */

export type LocalizedContent = {
  en: string;
  es: string;
  [key: string]: string;
};

export type SupportedLocale = 'en' | 'es';

/**
 * Get localized text from a localized content object
 * @param content - Object with localized text (e.g., { en: "Hello", es: "Hola" })
 * @param locale - Target locale (e.g., "en", "es")
 * @param fallbackLocale - Fallback locale if target is not found (default: "en")
 * @returns Localized text string
 */
export function getLocalizedText(
  content: LocalizedContent | string,
  locale: SupportedLocale = 'en',
  fallbackLocale: SupportedLocale = 'en'
): string {
  // If content is already a string, return it as-is
  if (typeof content === 'string') {
    return content;
  }

  // If content is null or undefined, return empty string
  if (!content) {
    return '';
  }

  // Try to get the requested locale
  if (content[locale]) {
    return content[locale];
  }

  // Fall back to fallback locale
  if (content[fallbackLocale]) {
    return content[fallbackLocale];
  }

  // If no localization found, return the first available value
  const firstKey = Object.keys(content)[0];
  return firstKey ? content[firstKey] : '';
}

/**
 * Get localized content for complex objects (like CTA buttons)
 * @param items - Array of items with localized content
 * @param locale - Target locale
 * @param fallbackLocale - Fallback locale
 * @returns Array with localized content
 */
export function getLocalizedArray<T extends Record<string, any>>(
  items: T[],
  locale: SupportedLocale = 'en',
  fallbackLocale: SupportedLocale = 'en'
): T[] {
  if (!Array.isArray(items)) {
    return [];
  }

  return items.map(item => {
    const localizedItem = { ...item };
    
    // Process each property in the item
    Object.keys(localizedItem).forEach(key => {
      const value = localizedItem[key];
      
      // If the value is a localized content object, localize it
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        // Check if this looks like a localized content object
        if (value.en || value.es) {
          localizedItem[key] = getLocalizedText(value, locale, fallbackLocale);
        }
      }
    });
    
    return localizedItem;
  });
}

/**
 * Get the current locale from astro-i18next
 * This function should be used in Astro components
 */
export function getCurrentLocale(): SupportedLocale {
  // This will be enhanced when we integrate with astro-i18next
  // For now, return default locale
  return 'en';
}

/**
 * Utility to check if a value is a localized content object
 * @param value - Value to check
 * @returns True if the value is a localized content object
 */
export function isLocalizedContent(value: any): value is LocalizedContent {
  return (
    value &&
    typeof value === 'object' &&
    !Array.isArray(value) &&
    (value.en || value.es)
  );
}

/**
 * Deep localize an object, converting all localized content to the target locale
 * @param obj - Object to localize
 * @param locale - Target locale
 * @param fallbackLocale - Fallback locale
 * @returns Localized object
 */
export function localizeObject<T extends Record<string, any>>(
  obj: T,
  locale: SupportedLocale = 'en',
  fallbackLocale: SupportedLocale = 'en'
): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const localized = { ...obj };

  Object.keys(localized).forEach(key => {
    const value = localized[key];

    if (isLocalizedContent(value)) {
      // This is a localized content object, extract the text
      localized[key] = getLocalizedText(value, locale, fallbackLocale);
    } else if (Array.isArray(value)) {
      // This is an array, process each item
      localized[key] = value.map(item => {
        if (typeof item === 'object' && item !== null) {
          return localizeObject(item, locale, fallbackLocale);
        }
        return item;
      });
    } else if (typeof value === 'object' && value !== null) {
      // This is a nested object, recurse
      localized[key] = localizeObject(value, locale, fallbackLocale);
    }
  });

  return localized;
}
