/**
 * Localization hook for Astro components
 * Integrates with astro-i18next to provide localized content
 */

import { getLocalizedText, localizeObject, type SupportedLocale, type LocalizedContent } from './localization';

/**
 * Localization utilities for Astro components
 */
export class LocalizationHelper {
  private locale: SupportedLocale;
  private fallbackLocale: SupportedLocale;

  constructor(locale: SupportedLocale = 'en', fallbackLocale: SupportedLocale = 'en') {
    this.locale = locale;
    this.fallbackLocale = fallbackLocale;
  }

  /**
   * Get localized text from content
   */
  t(content: LocalizedContent | string): string {
    return getLocalizedText(content, this.locale, this.fallbackLocale);
  }

  /**
   * Get localized content for a specific field path
   * @param data - The data object
   * @param path - Dot-separated path to the field (e.g., 'hero.title')
   */
  getField(data: any, path: string): string {
    const fieldContent = path.split('.').reduce((obj, key) => obj?.[key], data);
    return this.t(fieldContent);
  }

  /**
   * Localize an entire object
   */
  localizeObject<T extends Record<string, any>>(obj: T): T {
    return localizeObject(obj, this.locale, this.fallbackLocale);
  }

  /**
   * Get localized array items
   */
  localizeArray<T extends Record<string, any>>(items: T[]): T[] {
    if (!Array.isArray(items)) return [];
    
    return items.map(item => this.localizeObject(item));
  }

  /**
   * Get current locale
   */
  getLocale(): SupportedLocale {
    return this.locale;
  }

  /**
   * Set locale
   */
  setLocale(locale: SupportedLocale): void {
    this.locale = locale;
  }
}

/**
 * Create a localization helper instance
 * @param locale - Current locale
 * @param fallbackLocale - Fallback locale
 */
export function createLocalizationHelper(
  locale: SupportedLocale = 'en',
  fallbackLocale: SupportedLocale = 'en'
): LocalizationHelper {
  return new LocalizationHelper(locale, fallbackLocale);
}

/**
 * Get localized content from landing page data
 * This is a convenience function for common use cases
 */
export function getLocalizedLandingPageContent(
  landingPageData: any,
  locale: SupportedLocale = 'en'
) {
  const helper = createLocalizationHelper(locale);
  
  return {
    // Meta information
    meta: {
      title: helper.getField(landingPageData, 'meta.title'),
      description: helper.getField(landingPageData, 'meta.description'),
      keywords: helper.getField(landingPageData, 'meta.keywords'),
    },
    
    // Hero section
    hero: {
      title: helper.getField(landingPageData, 'hero.title'),
      tagline: helper.getField(landingPageData, 'hero.tagline'),
      backgroundImage: landingPageData.hero?.backgroundImage || '',
      appLogo: landingPageData.hero?.appLogo || '',
      videoUrl: landingPageData.hero?.videoUrl || '',
      ctaButtons: helper.localizeArray(landingPageData.hero?.ctaButtons || []),
    },
    
    // Features section
    features: {
      title: helper.getField(landingPageData, 'features.title'),
      items: helper.localizeArray(landingPageData.features?.items || []),
    },
    
    // How It Works section
    howItWorks: {
      title: helper.getField(landingPageData, 'howItWorks.title'),
      steps: helper.localizeArray(landingPageData.howItWorks?.steps || []),
    },
    
    // Social Proof section
    socialProof: {
      title: helper.getField(landingPageData, 'socialProof.title'),
      testimonials: helper.localizeArray(landingPageData.socialProof?.testimonials || []),
      metrics: helper.localizeObject(landingPageData.socialProof?.metrics || {}),
    },
    
    // Screenshots section
    screenshots: {
      title: helper.getField(landingPageData, 'screenshots.title'),
      images: landingPageData.screenshots?.images || [],
      videoUrl: landingPageData.screenshots?.videoUrl || '',
    },
    
    // CTA section
    cta: {
      title: helper.getField(landingPageData, 'cta.title'),
      description: helper.getField(landingPageData, 'cta.description'),
      buttons: helper.localizeArray(landingPageData.cta?.buttons || []),
    },
    
    // About section
    about: {
      title: helper.getField(landingPageData, 'about.title'),
      description: helper.getField(landingPageData, 'about.description'),
      teamImage: landingPageData.about?.teamImage || '',
    },
    
    // FAQ section
    faq: {
      title: helper.getField(landingPageData, 'faq.title'),
      items: helper.localizeArray(landingPageData.faq?.items || []),
    },
    
    // Footer section
    footer: {
      links: helper.localizeArray(landingPageData.footer?.links || []),
      socialMedia: landingPageData.footer?.socialMedia || [],
      appStoreBadges: landingPageData.footer?.appStoreBadges || [],
    },
    
    // Optional sections
    optional: {
      pricing: landingPageData.optional?.pricing?.enabled ? {
        enabled: true,
        title: helper.getField(landingPageData, 'optional.pricing.title'),
        plans: helper.localizeArray(landingPageData.optional.pricing?.plans || []),
      } : { enabled: false },
      
      newsletter: landingPageData.optional?.newsletter?.enabled ? {
        enabled: true,
        title: helper.getField(landingPageData, 'optional.newsletter.title'),
        description: helper.getField(landingPageData, 'optional.newsletter.description'),
        formAction: landingPageData.optional.newsletter?.formAction || '',
      } : { enabled: false },
    },
    
    // Utility functions
    helper,
  };
}
