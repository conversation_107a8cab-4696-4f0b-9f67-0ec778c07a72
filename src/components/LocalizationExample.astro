---
/**
 * Example component demonstrating localization usage
 * This shows how to use the localization utilities with landing page data
 */

import { getLocalizedLandingPageContent } from '../lib/useLocalization';
import type { SupportedLocale } from '../lib/localization';

// Import the landing page data
import landingPageData from '../data/userConfig.json';

export interface Props {
  locale?: SupportedLocale;
}

const { locale = 'en' } = Astro.props;

// Get localized content
const content = getLocalizedLandingPageContent(landingPageData.landingPage, locale);
---

<div class="localization-example p-6 max-w-4xl mx-auto">
  <h1 class="text-3xl font-bold mb-6">Localization Example (Locale: {locale})</h1>
  
  <!-- Meta Information -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">Meta Information</h2>
    <div class="bg-gray-100 p-4 rounded-lg">
      <p><strong>Title:</strong> {content.meta.title}</p>
      <p><strong>Description:</strong> {content.meta.description}</p>
      <p><strong>Keywords:</strong> {content.meta.keywords}</p>
    </div>
  </section>

  <!-- Hero Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">Hero Section</h2>
    <div class="bg-blue-50 p-4 rounded-lg">
      <h3 class="text-xl font-bold">{content.hero.title}</h3>
      <p class="text-gray-600 mb-4">{content.hero.tagline}</p>
      <div class="space-y-2">
        {content.hero.ctaButtons.map((button: any) => (
          <div class="inline-block mr-4">
            <span class="bg-blue-500 text-white px-4 py-2 rounded">
              {button.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">Features Section</h2>
    <div class="bg-green-50 p-4 rounded-lg">
      <h3 class="text-xl font-bold mb-4">{content.features.title}</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        {content.features.items.map((feature: any) => (
          <div class="bg-white p-4 rounded border">
            <h4 class="font-semibold">{feature.title}</h4>
            <p class="text-sm text-gray-600">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">How It Works Section</h2>
    <div class="bg-yellow-50 p-4 rounded-lg">
      <h3 class="text-xl font-bold mb-4">{content.howItWorks.title}</h3>
      <div class="space-y-4">
        {content.howItWorks.steps.map((step: any, index: number) => (
          <div class="flex items-start space-x-4">
            <div class="bg-yellow-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">
              {index + 1}
            </div>
            <div>
              <h4 class="font-semibold">{step.title}</h4>
              <p class="text-sm text-gray-600">{step.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">CTA Section</h2>
    <div class="bg-purple-50 p-4 rounded-lg text-center">
      <h3 class="text-xl font-bold">{content.cta.title}</h3>
      <p class="text-gray-600 mb-4">{content.cta.description}</p>
      <div class="space-x-4">
        {content.cta.buttons.map((button: any) => (
          <span class="bg-purple-500 text-white px-6 py-2 rounded">
            {button.text}
          </span>
        ))}
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">About Section</h2>
    <div class="bg-gray-50 p-4 rounded-lg">
      <h3 class="text-xl font-bold">{content.about.title}</h3>
      <p class="text-gray-600">{content.about.description}</p>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">FAQ Section</h2>
    <div class="bg-red-50 p-4 rounded-lg">
      <h3 class="text-xl font-bold mb-4">{content.faq.title}</h3>
      <div class="space-y-4">
        {content.faq.items.map((item: any) => (
          <div class="bg-white p-4 rounded border">
            <h4 class="font-semibold">{item.question}</h4>
            <p class="text-sm text-gray-600 mt-2">{item.answer}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Optional Sections -->
  {content.optional.pricing.enabled && (
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">Pricing Section (Optional)</h2>
      <div class="bg-indigo-50 p-4 rounded-lg">
        <h3 class="text-xl font-bold mb-4">{content.optional.pricing.title}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {content.optional.pricing.plans.map((plan: any) => (
            <div class="bg-white p-4 rounded border">
              <h4 class="font-semibold">{plan.name}</h4>
              <p class="text-lg font-bold text-indigo-600">{plan.price}</p>
              <ul class="text-sm text-gray-600 mt-2">
                {plan.features.map((feature: string) => (
                  <li>• {feature}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )}

  {content.optional.newsletter.enabled && (
    <section class="mb-8">
      <h2 class="text-2xl font-semibold mb-4">Newsletter Section (Optional)</h2>
      <div class="bg-teal-50 p-4 rounded-lg text-center">
        <h3 class="text-xl font-bold">{content.optional.newsletter.title}</h3>
        <p class="text-gray-600 mb-4">{content.optional.newsletter.description}</p>
        <div class="bg-teal-500 text-white px-6 py-2 rounded inline-block">
          Newsletter Signup Form
        </div>
      </div>
    </section>
  )}

  <!-- Language Toggle Demo -->
  <section class="mb-8">
    <h2 class="text-2xl font-semibold mb-4">Language Toggle Demo</h2>
    <div class="bg-gray-100 p-4 rounded-lg">
      <p class="mb-4">Current locale: <strong>{locale}</strong></p>
      <div class="space-x-4">
        <a href="?locale=en" class="bg-blue-500 text-white px-4 py-2 rounded">English</a>
        <a href="?locale=es" class="bg-green-500 text-white px-4 py-2 rounded">Español</a>
      </div>
    </div>
  </section>
</div>
