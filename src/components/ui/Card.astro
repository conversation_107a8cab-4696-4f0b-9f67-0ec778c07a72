---
/**
 * Enhanced Card Component
 * Follows shadcn design system with comprehensive variants and accessibility
 */

import { getCardClasses, cn } from '../../styles/component-variants';

export interface Props {
  variant?: 'default' | 'elevated' | 'interactive' | 'feature' | 'testimonial';
  padding?: 'none' | 'sm' | 'default' | 'lg' | 'xl';
  className?: string;
  hover?: boolean;
  clickable?: boolean;
  href?: string;
  target?: string;
  rel?: string;
  ariaLabel?: string;
  role?: string;
  tabIndex?: number;
}

const {
  variant = 'default',
  padding = 'default',
  className = '',
  hover = false,
  clickable = false,
  href,
  target,
  rel,
  ariaLabel,
  role,
  tabIndex,
  ...rest
} = Astro.props;

const cardClasses = cn(
  getCardClasses(variant, padding),
  hover && 'hover:shadow-lg transition-shadow duration-300',
  clickable && 'cursor-pointer hover:shadow-md transition-all duration-300',
  className
);

const Component = href ? 'a' : 'div';
const componentProps = href 
  ? { 
      href, 
      target, 
      rel: rel || (target === '_blank' ? 'noopener noreferrer' : undefined),
      role: role || 'link'
    }
  : { 
      role: clickable ? 'button' : role,
      tabIndex: clickable ? (tabIndex ?? 0) : tabIndex
    };
---

<Component
  class={cardClasses}
  aria-label={ariaLabel}
  {...componentProps}
  {...rest}
>
  <slot />
</Component>

<style>
  /* Enhanced card styles */
  .card-interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  .card-interactive:active {
    transform: translateY(-2px);
  }
  
  /* Focus styles for accessibility */
  div[role="button"]:focus-visible,
  a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .card-interactive,
    div,
    a {
      border: 2px solid #000;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .card-interactive {
      transition: none;
      transform: none !important;
    }
    
    .card-interactive:hover {
      transform: none;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    div,
    a {
      background-color: #1f2937;
      border-color: #374151;
      color: #f9fafb;
    }
  }
  
  /* Print styles */
  @media print {
    div,
    a {
      background: white !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
    }
  }
</style>

<script>
  // Enhanced card functionality
  document.addEventListener('DOMContentLoaded', function() {
    const clickableCards = document.querySelectorAll('[role="button"]');
    
    clickableCards.forEach(card => {
      // Keyboard navigation for clickable cards
      card.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });
      
      // Add visual feedback for interaction
      card.addEventListener('mousedown', function() {
        this.style.transform = 'translateY(-2px) scale(0.98)';
      });
      
      card.addEventListener('mouseup', function() {
        this.style.transform = '';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = '';
      });
    });
    
    // Intersection Observer for animation on scroll
    const animatedCards = document.querySelectorAll('.card-interactive');
    
    if ('IntersectionObserver' in window) {
      const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });
      
      animatedCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
        cardObserver.observe(card);
      });
    }
  });
</script>
