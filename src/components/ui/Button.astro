---
/**
 * Enhanced <PERSON><PERSON> Component
 * Follows shadcn design system with comprehensive variants and accessibility
 */

import { getButtonClasses, cn } from '../../styles/component-variants';

export interface Props {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'primary' | 'cta';
  size?: 'default' | 'sm' | 'lg' | 'xl' | 'icon';
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  href?: string;
  target?: string;
  rel?: string;
  type?: 'button' | 'submit' | 'reset';
  ariaLabel?: string;
  id?: string;
}

const {
  variant = 'default',
  size = 'default',
  className = '',
  disabled = false,
  loading = false,
  href,
  target,
  rel,
  type = 'button',
  ariaLabel,
  id,
  ...rest
} = Astro.props;

const buttonClasses = cn(
  getButtonClasses(variant, size),
  loading && 'opacity-70 cursor-not-allowed',
  disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
  className
);

const Component = href ? 'a' : 'button';
const componentProps = href 
  ? { href, target, rel: rel || (target === '_blank' ? 'noopener noreferrer' : undefined) }
  : { type, disabled: disabled || loading };
---

<Component
  class={buttonClasses}
  aria-label={ariaLabel}
  aria-disabled={disabled || loading}
  id={id}
  {...componentProps}
  {...rest}
>
  {loading && (
    <svg
      class="animate-spin -ml-1 mr-2 h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  )}
  <slot />
</Component>

<style>
  /* Additional button styles for enhanced variants */
  .btn-cta {
    position: relative;
    overflow: hidden;
  }
  
  .btn-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  .btn-cta:hover::before {
    left: 100%;
  }
  
  /* Focus styles for better accessibility */
  button:focus-visible,
  a:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    button,
    a {
      border: 2px solid currentColor;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    button,
    a {
      transition: none;
      transform: none !important;
    }
    
    .btn-cta::before {
      display: none;
    }
    
    .animate-spin {
      animation: none;
    }
  }
  
  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    button,
    a {
      min-height: 44px; /* Minimum touch target size */
      min-width: 44px;
    }
  }
  
  /* Print styles */
  @media print {
    button,
    a {
      background: transparent !important;
      color: black !important;
      border: 1px solid black !important;
      box-shadow: none !important;
    }
  }
</style>

<script>
  // Enhanced button functionality
  document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('button, a[role="button"]');
    
    buttons.forEach(button => {
      // Add ripple effect for touch devices
      button.addEventListener('click', function(e) {
        if (this.disabled || this.getAttribute('aria-disabled') === 'true') {
          e.preventDefault();
          return;
        }
        
        // Create ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s linear;
          pointer-events: none;
        `;
        
        this.style.position = 'relative';
        this.style.overflow = 'hidden';
        this.appendChild(ripple);
        
        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
      
      // Keyboard navigation enhancement
      button.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          if (this.disabled || this.getAttribute('aria-disabled') === 'true') {
            e.preventDefault();
            return;
          }
          
          // Add visual feedback for keyboard activation
          this.style.transform = 'scale(0.98)';
          setTimeout(() => {
            this.style.transform = '';
          }, 100);
        }
      });
    });
  });
  
  // Add ripple animation CSS
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  `;
  document.head.appendChild(style);
</script>
