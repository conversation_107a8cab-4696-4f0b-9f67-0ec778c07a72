---
/**
 * Language Switcher Component
 * Allows users to toggle between supported languages (English and Spanish)
 * Uses native HTML buttons styled with Tailwind CSS to match shadcn design
 */

import type { SupportedLocale } from '../lib/localization';

export interface Props {
  currentLocale?: SupportedLocale;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showLabels?: boolean;
  className?: string;
}

const {
  currentLocale = 'en',
  variant = 'outline',
  size = 'default',
  showLabels = true,
  className = '',
} = Astro.props;

// Language configuration
const languages = [
  {
    code: 'en' as SupportedLocale,
    name: 'English',
    flag: '🇺🇸',
    shortName: 'EN'
  },
  {
    code: 'es' as SupportedLocale,
    name: 'Espa<PERSON><PERSON>',
    flag: '🇪🇸',
    shortName: 'ES'
  }
];

// Get current URL for language switching
const currentUrl = new URL(Astro.request.url);

// Function to generate language switch URL
function getLanguageUrl(locale: SupportedLocale): string {
  const url = new URL(currentUrl);
  url.searchParams.set('locale', locale);
  return url.toString();
}

// Generate button classes based on variant and size
function getButtonClasses(isActive: boolean, variant: string, size: string): string {
  const baseClasses = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";

  const variantClasses = isActive ?
    "bg-primary text-primary-foreground hover:bg-primary/90" :
    variant === 'outline' ? "border border-input bg-background hover:bg-accent hover:text-accent-foreground" :
    variant === 'secondary' ? "bg-secondary text-secondary-foreground hover:bg-secondary/80" :
    variant === 'ghost' ? "hover:bg-accent hover:text-accent-foreground" :
    "bg-primary text-primary-foreground hover:bg-primary/90";

  const sizeClasses =
    size === 'sm' ? "h-9 rounded-md px-3" :
    size === 'lg' ? "h-11 rounded-md px-8" :
    size === 'icon' ? "h-10 w-10" :
    "h-10 px-4 py-2";

  return `${baseClasses} ${variantClasses} ${sizeClasses}`;
}
---

<div class={`language-switcher flex items-center gap-2 ${className}`}>
  {languages.map((language) => {
    const isActive = currentLocale === language.code;
    const href = getLanguageUrl(language.code);
    const buttonClasses = getButtonClasses(isActive, variant, size);

    return (
      <a
        href={href}
        class="language-switch-link"
        aria-label={`Switch to ${language.name}`}
        data-locale={language.code}
      >
        <button
          class={`language-button ${buttonClasses} ${isActive ? 'active' : ''}`}
          type="button"
        >
          <span class="flag" aria-hidden="true">{language.flag}</span>
          {showLabels && (
            <span class="language-name ml-2">
              {language.shortName}
            </span>
          )}
        </button>
      </a>
    );
  })}
</div>

<style>
  .language-switcher {
    user-select: none;
  }

  .language-switch-link {
    text-decoration: none;
    display: inline-block;
  }

  .language-switch-link:hover {
    text-decoration: none;
  }

  .language-button {
    transition: all 0.2s ease-in-out;
    border: 1px solid hsl(var(--border));
  }

  .language-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .language-button.active {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-color: hsl(var(--primary));
  }

  .flag {
    font-size: 1.1em;
    line-height: 1;
  }

  .language-name {
    font-weight: 500;
    font-size: 0.875rem;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .language-name {
      display: none;
    }
    
    .flag {
      font-size: 1.2em;
    }
  }

  /* Focus styles for accessibility */
  .language-switch-link:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .language-button {
      border-width: 2px;
    }
    
    .language-button.active {
      border-width: 3px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .language-button {
      transition: none;
    }
    
    .language-button:hover {
      transform: none;
    }
  }
</style>

<script>
  // Client-side language switching functionality
  document.addEventListener('DOMContentLoaded', function() {
    const languageButtons = document.querySelectorAll('.language-switch-link');

    languageButtons.forEach(button => {
      button.addEventListener('click', function() {
        const locale = (this as HTMLElement).getAttribute('data-locale');

        // Store the selected locale in localStorage for persistence
        if (locale) {
          localStorage.setItem('preferred-locale', locale);
        }

        // Add loading state
        const buttonElement = (this as HTMLElement).querySelector('.language-button') as HTMLElement;
        if (buttonElement) {
          buttonElement.style.opacity = '0.7';
          buttonElement.style.pointerEvents = 'none';
        }

        // The navigation will happen via the href attribute
        // This script just adds some UX enhancements
      });
    });

    // Check for stored locale preference on page load
    const storedLocale = localStorage.getItem('preferred-locale');
    const currentUrl = new URL(window.location.href);
    const urlLocale = currentUrl.searchParams.get('locale');

    // If there's a stored preference and no URL locale, redirect
    if (storedLocale && !urlLocale && storedLocale !== 'en') {
      currentUrl.searchParams.set('locale', storedLocale);
      window.location.href = currentUrl.toString();
    }
  });
</script>
