---
/**
 * About Section Component
 * Displays information about the app/company with team image
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Extract localized content
const title = getLocalizedText(data.title, locale);
const description = getLocalizedText(data.description, locale);
---

<section class={`about-section py-16 md:py-24 bg-gray-50 ${className}`}>
  <div class="container mx-auto px-4">
    
    <!-- Section Title -->
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
        {title}
      </h2>
      <div class="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
    </div>
    
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        
        <!-- Content -->
        <div class="space-y-6">
          <div class="prose prose-lg max-w-none">
            <p class="text-gray-700 leading-relaxed text-lg">
              {description}
            </p>
          </div>
          
          <!-- Key Points -->
          <div class="space-y-4">
            <div class="flex items-start gap-4">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-1">User-Focused Design</h3>
                <p class="text-gray-600">Every feature is designed with user experience in mind</p>
              </div>
            </div>
            
            <div class="flex items-start gap-4">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-1">Continuous Innovation</h3>
                <p class="text-gray-600">Regular updates and new features based on user feedback</p>
              </div>
            </div>
            
            <div class="flex items-start gap-4">
              <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 mb-1">Privacy & Security</h3>
                <p class="text-gray-600">Your data is protected with industry-leading security measures</p>
              </div>
            </div>
          </div>
          
          <!-- Stats -->
          <div class="grid grid-cols-3 gap-6 pt-8 border-t border-gray-200">
            <div class="text-center">
              <div class="text-2xl md:text-3xl font-bold text-blue-600 mb-1">5+</div>
              <div class="text-sm text-gray-600">Years Experience</div>
            </div>
            <div class="text-center">
              <div class="text-2xl md:text-3xl font-bold text-blue-600 mb-1">1M+</div>
              <div class="text-sm text-gray-600">Happy Users</div>
            </div>
            <div class="text-center">
              <div class="text-2xl md:text-3xl font-bold text-blue-600 mb-1">50+</div>
              <div class="text-sm text-gray-600">Countries</div>
            </div>
          </div>
        </div>
        
        <!-- Team Image -->
        <div class="relative">
          {data.teamImage ? (
            <div class="relative">
              <img 
                src={data.teamImage} 
                alt="Our Team"
                class="w-full h-auto rounded-2xl shadow-2xl"
                loading="lazy"
              />
              
              <!-- Overlay with team info -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-2xl"></div>
              <div class="absolute bottom-6 left-6 right-6 text-white">
                <h3 class="text-xl font-semibold mb-2">Meet Our Team</h3>
                <p class="text-sm text-gray-200">Passionate developers and designers working to create amazing experiences</p>
              </div>
            </div>
          ) : (
            <!-- Placeholder if no team image -->
            <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-12 text-white text-center shadow-2xl">
              <div class="w-24 h-24 bg-white/20 rounded-full mx-auto mb-6 flex items-center justify-center">
                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <h3 class="text-2xl font-bold mb-4">Our Mission</h3>
              <p class="text-blue-100 leading-relaxed">
                To create innovative solutions that make people's lives easier and more productive.
              </p>
            </div>
          )}
          
          <!-- Decorative Elements -->
          <div class="absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20 blur-xl"></div>
          <div class="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-20 blur-xl"></div>
        </div>
        
      </div>
    </div>
    
    <!-- Company Values -->
    <div class="mt-20 max-w-4xl mx-auto">
      <h3 class="text-2xl md:text-3xl font-bold text-center text-gray-900 mb-12">Our Values</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center group">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors duration-300">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <h4 class="text-lg font-semibold text-gray-900 mb-2">Passion</h4>
          <p class="text-gray-600 text-sm">We love what we do and it shows in every detail</p>
        </div>
        
        <div class="text-center group">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors duration-300">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h4 class="text-lg font-semibold text-gray-900 mb-2">Quality</h4>
          <p class="text-gray-600 text-sm">Excellence in every feature and interaction</p>
        </div>
        
        <div class="text-center group">
          <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors duration-300">
            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h4 class="text-lg font-semibold text-gray-900 mb-2">Innovation</h4>
          <p class="text-gray-600 text-sm">Always pushing boundaries and exploring new possibilities</p>
        </div>
      </div>
    </div>
    
  </div>
</section>

<style>
  /* Animation for stats */
  .about-section .text-2xl,
  .about-section .text-3xl {
    opacity: 0;
    transform: translateY(20px);
    animation: statsFadeIn 0.8s ease-out forwards;
  }
  
  .about-section .text-2xl:nth-child(1) { animation-delay: 0.2s; }
  .about-section .text-2xl:nth-child(2) { animation-delay: 0.4s; }
  .about-section .text-2xl:nth-child(3) { animation-delay: 0.6s; }
  
  @keyframes statsFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .about-section {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .about-section .bg-blue-100,
    .about-section .bg-green-100,
    .about-section .bg-purple-100 {
      border: 1px solid currentColor;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .about-section .text-2xl,
    .about-section .text-3xl {
      animation: none;
      opacity: 1;
      transform: none;
    }
    
    .about-section .group:hover > div {
      background-color: inherit;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .about-section {
      background-color: #1f2937;
    }
    
    .about-section h2,
    .about-section h3,
    .about-section h4 {
      color: #f9fafb;
    }
    
    .about-section p {
      color: #d1d5db;
    }
    
    .about-section .border-gray-200 {
      border-color: #374151;
    }
  }
</style>
