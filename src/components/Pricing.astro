---
/**
 * Pricing Section Component (Optional)
 * Displays pricing plans when enabled
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Only render if pricing is enabled
if (!data.enabled) {
  return null;
}

// Extract localized content
const title = getLocalizedText(data.title, locale);
const plans = data.plans?.map((plan: any) => ({
  ...plan,
  name: getLocalizedText(plan.name, locale),
  description: getLocalizedText(plan.description, locale),
  features: plan.features?.map((feature: any) => 
    typeof feature === 'string' ? feature : getLocalizedText(feature, locale)
  ) || []
})) || [];
---

<section class={`pricing-section py-16 md:py-24 bg-gray-50 ${className}`}>
  <div class="container mx-auto px-4">
    
    <!-- Section Title -->
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
        {title}
      </h2>
      <div class="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
      <p class="text-gray-600 mt-6 text-lg max-w-2xl mx-auto">
        Choose the perfect plan for your needs. Upgrade or downgrade at any time.
      </p>
    </div>
    
    <!-- Pricing Plans -->
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {plans.map((plan: any, index: number) => (
          <div class={`pricing-card relative ${plan.featured ? 'featured' : ''}`}>
            
            <!-- Featured Badge -->
            {plan.featured && (
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div class="bg-blue-600 text-white px-6 py-2 rounded-full text-sm font-semibold">
                  Most Popular
                </div>
              </div>
            )}
            
            <!-- Card Content -->
            <div class={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col ${plan.featured ? 'ring-2 ring-blue-600 transform scale-105' : ''}`}>
              
              <!-- Plan Header -->
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <p class="text-gray-600 mb-6">
                  {plan.description}
                </p>
                
                <!-- Price -->
                <div class="mb-6">
                  <div class="flex items-baseline justify-center">
                    <span class="text-5xl font-bold text-gray-900">
                      {plan.price}
                    </span>
                    {plan.period && (
                      <span class="text-gray-600 ml-2">
                        /{plan.period}
                      </span>
                    )}
                  </div>
                  {plan.originalPrice && (
                    <div class="text-gray-500 line-through text-lg mt-1">
                      {plan.originalPrice}
                    </div>
                  )}
                </div>
              </div>
              
              <!-- Features List -->
              <div class="flex-1 mb-8">
                <ul class="space-y-4">
                  {plan.features.map((feature: string) => (
                    <li class="flex items-start gap-3">
                      <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <span class="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <!-- CTA Button -->
              <div class="text-center">
                <a 
                  href={plan.ctaUrl || '#subscribe'}
                  class={`inline-flex items-center justify-center w-full px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 ${
                    plan.featured 
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg' 
                      : 'bg-gray-900 hover:bg-gray-800 text-white'
                  }`}
                >
                  {plan.ctaText || 'Get Started'}
                </a>
                
                {plan.trialText && (
                  <p class="text-gray-500 text-sm mt-3">
                    {plan.trialText}
                  </p>
                )}
              </div>
              
            </div>
          </div>
        ))}
      </div>
    </div>
    
    <!-- Additional Info -->
    <div class="text-center mt-16">
      <div class="bg-blue-50 rounded-2xl p-8 max-w-4xl mx-auto">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">
          All plans include:
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-700">
          <div class="flex items-center justify-center gap-2">
            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>30-day money-back guarantee</span>
          </div>
          
          <div class="flex items-center justify-center gap-2">
            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
            <span>24/7 customer support</span>
          </div>
          
          <div class="flex items-center justify-center gap-2">
            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
            </svg>
            <span>Cancel anytime</span>
          </div>
        </div>
        
        <div class="mt-8">
          <p class="text-gray-600 mb-4">
            Need a custom plan for your organization?
          </p>
          <a 
            href="#contact"
            class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
          >
            <span>Contact our sales team</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </a>
        </div>
      </div>
    </div>
    
  </div>
</section>

<style>
  /* Pricing card animation */
  .pricing-card {
    opacity: 0;
    transform: translateY(20px);
    animation: pricingFadeIn 0.6s ease-out forwards;
  }
  
  .pricing-card:nth-child(1) { animation-delay: 0.2s; }
  .pricing-card:nth-child(2) { animation-delay: 0.4s; }
  .pricing-card:nth-child(3) { animation-delay: 0.6s; }
  
  @keyframes pricingFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Featured plan styling */
  .pricing-card.featured {
    z-index: 10;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .pricing-section {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
    
    .pricing-card.featured > div {
      transform: none;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .pricing-card > div {
      border: 2px solid #333;
    }
    
    .pricing-card.featured > div {
      border-color: #3b82f6;
      border-width: 3px;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .pricing-card {
      animation: none;
      opacity: 1;
      transform: none;
    }
    
    .pricing-card > div {
      transform: none !important;
    }
    
    .pricing-card a {
      transform: none !important;
    }
  }
  
  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .pricing-section {
      background-color: #1f2937;
    }
    
    .pricing-card > div {
      background-color: #374151;
    }
    
    .pricing-section h2,
    .pricing-section h3 {
      color: #f9fafb;
    }
    
    .pricing-section p {
      color: #d1d5db;
    }
  }
</style>
