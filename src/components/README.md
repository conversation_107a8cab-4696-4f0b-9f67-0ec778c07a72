# Language Switcher Components

This directory contains language switcher components for the mobile app landing page.

## Components

### LanguageSwitcher.astro

A button-style language switcher that displays language options as individual buttons.

**Features:**
- Button-style interface with flags and language codes
- Supports English (EN) and Spanish (ES)
- Responsive design (hides labels on mobile)
- Accessibility support (ARIA labels, keyboard navigation)
- Local storage persistence for user preference
- Styled with Tailwind CSS to match shadcn design system

**Usage:**
```astro
---
import LanguageSwitcher from '../components/LanguageSwitcher.astro';
---

<LanguageSwitcher 
  currentLocale="en" 
  variant="outline" 
  size="default" 
  showLabels={true}
/>
```

**Props:**
- `currentLocale?: SupportedLocale` - Current active locale (default: 'en')
- `variant?: string` - Button style variant (default: 'outline')
- `size?: string` - Button size (default: 'default')
- `showLabels?: boolean` - Show language labels (default: true)
- `className?: string` - Additional CSS classes

### LanguageSwitcherDropdown.astro

A dropdown-style language switcher for when more languages are supported.

**Features:**
- Dropdown interface with current language display
- Full language names with native names
- Keyboard navigation support (Arrow keys, Enter, Escape)
- Click outside to close
- Accessibility compliant (ARIA attributes, focus management)
- Visual indicators for active language
- Local storage persistence

**Usage:**
```astro
---
import LanguageSwitcherDropdown from '../components/LanguageSwitcherDropdown.astro';
---

<LanguageSwitcherDropdown 
  currentLocale="es" 
  variant="outline" 
  size="default"
/>
```

**Props:**
- `currentLocale?: SupportedLocale` - Current active locale (default: 'en')
- `variant?: string` - Button style variant (default: 'outline')
- `size?: string` - Button size (default: 'default')
- `className?: string` - Additional CSS classes

## Language Configuration

Both components support the following languages:

```typescript
const languages = [
  {
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    shortName: 'EN',
    nativeName: 'English'
  },
  {
    code: 'es',
    name: 'Español',
    flag: '🇪🇸',
    shortName: 'ES',
    nativeName: 'Español'
  }
];
```

## URL-based Language Switching

The components use URL parameters for language switching:
- `/page?locale=en` - English
- `/page?locale=es` - Spanish

## Local Storage Persistence

User language preferences are stored in `localStorage` with the key `preferred-locale`. The components automatically redirect to the stored preference if no URL parameter is present.

## Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Proper focus handling in dropdown
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user's motion preferences

## Styling

Components use Tailwind CSS classes and CSS custom properties that match the shadcn design system:
- `--primary` / `--primary-foreground`
- `--secondary` / `--secondary-foreground`
- `--accent` / `--accent-foreground`
- `--border` / `--input`
- `--ring`

## Integration with astro-i18next

The components are designed to work with astro-i18next for complete internationalization. The current locale can be obtained from astro-i18next and passed to the components.

## Adding New Languages

To add a new language:

1. Add the language to the `languages` array in both components
2. Update the `SupportedLocale` type in `src/lib/localization.ts`
3. Create the corresponding locale file in `src/locales/`
4. Update `astro-i18next.config.mjs` to include the new locale

## Testing

Visit `/localization-test` to see both language switcher styles in action and test the functionality.
