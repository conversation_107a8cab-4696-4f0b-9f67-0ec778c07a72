---
/**
 * Newsletter Section Component (Optional)
 * Displays newsletter signup form when enabled
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Only render if newsletter is enabled
if (!data.enabled) {
  return null;
}

// Extract localized content
const title = getLocalizedText(data.title, locale);
const description = getLocalizedText(data.description, locale);
const formAction = data.formAction || '#newsletter';
---

<section class={`newsletter-section py-16 md:py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden ${className}`}>
  
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.3\"%3E%3Cpath d=\"M20 20c0 11.046-8.954 20-20 20v20h40V20H20z\"/%3E%3C/g%3E%3C/svg%3E');"></div>
  </div>
  
  <!-- Floating Elements -->
  <div class="absolute top-10 left-10 w-16 h-16 bg-white/10 rounded-full blur-xl animate-float"></div>
  <div class="absolute bottom-10 right-10 w-24 h-24 bg-white/10 rounded-full blur-xl animate-float-delayed"></div>
  
  <div class="container mx-auto px-4 relative z-10">
    <div class="max-w-4xl mx-auto text-center text-white">
      
      <!-- Icon -->
      <div class="mb-8">
        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto">
          <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
        </div>
      </div>
      
      <!-- Title -->
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
        {title}
      </h2>
      
      <!-- Description -->
      {description && (
        <p class="text-xl md:text-2xl mb-12 text-blue-100 max-w-3xl mx-auto leading-relaxed">
          {description}
        </p>
      )}
      
      <!-- Newsletter Form -->
      <div class="max-w-2xl mx-auto">
        <form 
          action={formAction}
          method="POST"
          class="newsletter-form"
          id="newsletter-form"
        >
          <div class="flex flex-col sm:flex-row gap-4 mb-6">
            
            <!-- Email Input -->
            <div class="flex-1">
              <label for="newsletter-email" class="sr-only">Email address</label>
              <input 
                type="email" 
                id="newsletter-email"
                name="email"
                placeholder="Enter your email address"
                required
                class="w-full px-6 py-4 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300"
              />
            </div>
            
            <!-- Submit Button -->
            <button 
              type="submit"
              class="px-8 py-4 bg-white text-blue-600 font-semibold rounded-xl hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Subscribe
            </button>
            
          </div>
          
          <!-- Privacy Notice -->
          <p class="text-blue-200 text-sm">
            By subscribing, you agree to our 
            <a href="/privacy" class="underline hover:text-white transition-colors duration-200">
              Privacy Policy
            </a>
            and consent to receive updates from our team.
          </p>
          
        </form>
        
        <!-- Success Message -->
        <div id="newsletter-success" class="hidden bg-green-500/20 border border-green-400 rounded-xl p-6 mt-6">
          <div class="flex items-center gap-3">
            <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
              <h3 class="font-semibold text-green-100">Successfully subscribed!</h3>
              <p class="text-green-200 text-sm">Thank you for joining our newsletter. Check your email for confirmation.</p>
            </div>
          </div>
        </div>
        
        <!-- Error Message -->
        <div id="newsletter-error" class="hidden bg-red-500/20 border border-red-400 rounded-xl p-6 mt-6">
          <div class="flex items-center gap-3">
            <svg class="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
            <div>
              <h3 class="font-semibold text-red-100">Subscription failed</h3>
              <p class="text-red-200 text-sm">Please try again or contact support if the problem persists.</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Benefits -->
      <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
        <div class="text-center">
          <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Weekly Updates</h3>
          <p class="text-blue-200 text-sm">Get the latest features and improvements delivered to your inbox</p>
        </div>
        
        <div class="text-center">
          <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Exclusive Tips</h3>
          <p class="text-blue-200 text-sm">Learn productivity tips and tricks from our expert team</p>
        </div>
        
        <div class="text-center">
          <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd" />
            </svg>
          </div>
          <h3 class="font-semibold mb-2">Early Access</h3>
          <p class="text-blue-200 text-sm">Be the first to try new features before they're released</p>
        </div>
      </div>
      
    </div>
  </div>
</section>

<style>
  /* Floating animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-30px); }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
    animation-delay: 2s;
  }
  
  /* Form styling */
  .newsletter-form input:focus {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .newsletter-section {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .newsletter-section {
      background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #1e40af 100%);
    }
    
    .newsletter-form input,
    .newsletter-form button {
      border: 2px solid #333;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-float-delayed {
      animation: none;
    }
    
    .newsletter-form button {
      transform: none !important;
    }
  }
  
  /* Print styles */
  @media print {
    .newsletter-section {
      background: white !important;
      color: black !important;
    }
    
    .newsletter-section * {
      color: black !important;
    }
    
    .animate-float,
    .animate-float-delayed {
      display: none;
    }
  }
</style>

<script>
  // Newsletter form functionality
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('newsletter-form');
    const successMessage = document.getElementById('newsletter-success');
    const errorMessage = document.getElementById('newsletter-error');
    
    if (!form || !successMessage || !errorMessage) return;
    
    form.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const formData = new FormData(form);
      const email = formData.get('email');
      
      // Hide previous messages
      successMessage.classList.add('hidden');
      errorMessage.classList.add('hidden');
      
      // Basic email validation
      if (!email || !email.includes('@')) {
        errorMessage.classList.remove('hidden');
        return;
      }
      
      try {
        // Submit form (replace with actual endpoint)
        const response = await fetch(form.action, {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json'
          }
        });
        
        if (response.ok) {
          successMessage.classList.remove('hidden');
          form.reset();
        } else {
          throw new Error('Subscription failed');
        }
      } catch (error) {
        console.error('Newsletter subscription error:', error);
        errorMessage.classList.remove('hidden');
      }
    });
  });
</script>
