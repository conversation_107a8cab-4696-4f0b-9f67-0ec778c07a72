---
/**
 * Language Switcher Dropdown Component
 * A dropdown version of the language switcher for when more languages are supported
 * Uses native HTML button styled with Tailwind CSS to match shadcn design
 */

import type { SupportedLocale } from '../lib/localization';

export interface Props {
  currentLocale?: SupportedLocale;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

const {
  currentLocale = 'en',
  variant = 'outline',
  size = 'default',
  className = '',
} = Astro.props;

// Language configuration
const languages = [
  {
    code: 'en' as SupportedLocale,
    name: 'English',
    flag: '🇺🇸',
    nativeName: 'English'
  },
  {
    code: 'es' as SupportedLocale,
    name: 'Español',
    flag: '🇪🇸',
    nativeName: 'Español'
  }
];

// Get current language info
const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

// Get current URL for language switching
const currentUrl = new URL(Astro.request.url);

// Function to generate language switch URL
function getLanguageUrl(locale: SupportedLocale): string {
  const url = new URL(currentUrl);
  url.searchParams.set('locale', locale);
  return url.toString();
}

// Generate button classes based on variant and size
function getButtonClasses(variant: string, size: string): string {
  const baseClasses = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";

  const variantClasses =
    variant === 'outline' ? "border border-input bg-background hover:bg-accent hover:text-accent-foreground" :
    variant === 'secondary' ? "bg-secondary text-secondary-foreground hover:bg-secondary/80" :
    variant === 'ghost' ? "hover:bg-accent hover:text-accent-foreground" :
    "bg-primary text-primary-foreground hover:bg-primary/90";

  const sizeClasses =
    size === 'sm' ? "h-9 rounded-md px-3" :
    size === 'lg' ? "h-11 rounded-md px-8" :
    size === 'icon' ? "h-10 w-10" :
    "h-10 px-4 py-2";

  return `${baseClasses} ${variantClasses} ${sizeClasses}`;
}
---

<div class={`language-switcher-dropdown relative ${className}`}>
  <!-- Current Language Button -->
  <button
    class={`language-dropdown-trigger flex items-center gap-2 ${getButtonClasses(variant, size)}`}
    id="language-dropdown-button"
    aria-haspopup="true"
    aria-expanded="false"
    type="button"
  >
    <span class="flag" aria-hidden="true">{currentLanguage.flag}</span>
    <span class="language-name">{currentLanguage.nativeName}</span>
    <svg
      class="dropdown-arrow w-4 h-4 transition-transform duration-200"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div
    class="language-dropdown-menu absolute top-full left-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-50 opacity-0 invisible transition-all duration-200"
    id="language-dropdown-menu"
    role="menu"
    aria-labelledby="language-dropdown-button"
  >
    {languages.map((language) => {
      const isActive = currentLocale === language.code;
      const href = getLanguageUrl(language.code);
      
      return (
        <a
          href={href}
          class={`language-option flex items-center gap-3 px-4 py-3 text-sm hover:bg-gray-50 transition-colors ${
            isActive ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
          }`}
          role="menuitem"
          data-locale={language.code}
        >
          <span class="flag text-lg" aria-hidden="true">{language.flag}</span>
          <div class="flex flex-col">
            <span class="font-medium">{language.nativeName}</span>
            <span class="text-xs text-gray-500">{language.name}</span>
          </div>
          {isActive && (
            <svg
              class="w-4 h-4 ml-auto text-blue-600"
              fill="currentColor"
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          )}
        </a>
      );
    })}
  </div>
</div>

<style>
  .language-switcher-dropdown {
    user-select: none;
  }

  .language-dropdown-trigger {
    min-width: 120px;
    justify-content: space-between;
  }

  .language-dropdown-menu {
    min-width: 160px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .language-dropdown-menu.open {
    opacity: 1;
    visibility: visible;
  }

  .dropdown-arrow.open {
    transform: rotate(180deg);
  }

  .language-option {
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
  }

  .language-option:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
  }

  .language-option:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
  }

  .flag {
    line-height: 1;
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .language-dropdown-menu {
      background-color: hsl(var(--background));
      border-color: hsl(var(--border));
    }
    
    .language-option {
      color: hsl(var(--foreground));
    }
    
    .language-option:hover {
      background-color: hsl(var(--accent));
    }
  }

  /* Focus styles for accessibility */
  .language-dropdown-trigger:focus-visible,
  .language-option:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .language-dropdown-menu,
    .dropdown-arrow {
      transition: none;
    }
  }
</style>

<script>
  // Dropdown functionality
  document.addEventListener('DOMContentLoaded', function() {
    const trigger = document.getElementById('language-dropdown-button') as HTMLButtonElement;
    const menu = document.getElementById('language-dropdown-menu') as HTMLDivElement;
    const arrow = trigger?.querySelector('.dropdown-arrow') as HTMLElement;

    if (!trigger || !menu) return;

    let isOpen = false;

    function toggleDropdown() {
      isOpen = !isOpen;

      if (isOpen) {
        menu.classList.add('open');
        arrow?.classList.add('open');
        trigger.setAttribute('aria-expanded', 'true');
      } else {
        menu.classList.remove('open');
        arrow?.classList.remove('open');
        trigger.setAttribute('aria-expanded', 'false');
      }
    }

    function closeDropdown() {
      if (isOpen) {
        isOpen = false;
        menu.classList.remove('open');
        arrow?.classList.remove('open');
        trigger.setAttribute('aria-expanded', 'false');
      }
    }
    
    // Toggle dropdown on button click
    trigger.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      toggleDropdown();
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!trigger.contains(e.target as Node) && !menu.contains(e.target as Node)) {
        closeDropdown();
      }
    });
    
    // Handle keyboard navigation
    trigger.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        toggleDropdown();
      } else if (e.key === 'Escape') {
        closeDropdown();
      } else if (e.key === 'ArrowDown' && isOpen) {
        e.preventDefault();
        const firstOption = menu.querySelector('.language-option') as HTMLElement;
        firstOption?.focus();
      }
    });
    
    // Handle option selection and keyboard navigation within menu
    const options = menu.querySelectorAll('.language-option');
    options.forEach((option, index) => {
      option.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          const nextOption = options[index + 1] as HTMLElement || options[0] as HTMLElement;
          nextOption.focus();
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          const prevOption = options[index - 1] as HTMLElement || options[options.length - 1] as HTMLElement;
          prevOption.focus();
        } else if (e.key === 'Escape') {
          closeDropdown();
          trigger.focus();
        } else if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          (option as HTMLElement).click();
        }
      });

      option.addEventListener('click', function() {
        const locale = (this as HTMLElement).getAttribute('data-locale');

        // Store the selected locale in localStorage for persistence
        if (locale) {
          localStorage.setItem('preferred-locale', locale);
        }

        closeDropdown();
      });
    });
  });
</script>
