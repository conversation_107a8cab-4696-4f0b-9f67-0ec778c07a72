---
/**
 * LocalizedContent component for rendering localized content from JSON data
 * This component integrates with astro-i18next to get the current locale
 * and renders content based on the selected language
 */

import { getLocalizedText, localizeObject, type SupportedLocale } from '../lib/localization';

export interface Props {
  content: any;
  field?: string;
  locale?: SupportedLocale;
  fallbackLocale?: SupportedLocale;
  as?: keyof HTMLElementTagNameMap;
  class?: string;
  [key: string]: any;
}

const {
  content,
  field,
  locale = 'en',
  fallbackLocale = 'en',
  as: Tag = 'span',
  class: className,
  ...rest
} = Astro.props;

// Get the localized text
let localizedText = '';

if (field) {
  // If a specific field is requested, extract it from the content
  const fieldContent = field.split('.').reduce((obj, key) => obj?.[key], content);
  localizedText = getLocalizedText(fieldContent, locale, fallbackLocale);
} else if (typeof content === 'object' && (content.en || content.es)) {
  // If content is a localized object, extract the text directly
  localizedText = getLocalizedText(content, locale, fallbackLocale);
} else if (typeof content === 'string') {
  // If content is already a string, use it as-is
  localizedText = content;
} else {
  // For complex objects, try to localize the entire object
  const localized = localizeObject(content, locale, fallbackLocale);
  localizedText = JSON.stringify(localized);
}
---

<Tag class={className} {...rest}>
  {localizedText}
</Tag>
