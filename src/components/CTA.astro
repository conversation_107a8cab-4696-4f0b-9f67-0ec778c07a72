---
/**
 * Call to Action Section Component
 * Displays main CTA with download buttons and app store badges
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Extract localized content
const title = getLocalizedText(data.title, locale);
const description = getLocalizedText(data.description, locale);
const buttons = data.buttons?.map((button: any) => ({
  ...button,
  text: getLocalizedText(button.text, locale)
})) || [];
---

<section class={`cta-section py-16 md:py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden ${className}`}>
  
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.4\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
  </div>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-float"></div>
  <div class="absolute bottom-20 right-10 w-32 h-32 bg-white/10 rounded-full blur-xl animate-float-delayed"></div>
  <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-xl animate-float-slow"></div>
  
  <div class="container mx-auto px-4 relative z-10">
    <div class="max-w-4xl mx-auto text-center text-white">
      
      <!-- Title -->
      <h2 class="text-3xl md:text-4xl lg:text-6xl font-bold mb-6 leading-tight">
        {title}
      </h2>
      
      <!-- Description -->
      {description && (
        <p class="text-xl md:text-2xl mb-12 text-blue-100 max-w-3xl mx-auto leading-relaxed">
          {description}
        </p>
      )}
      
      <!-- Download Buttons -->
      {buttons.length > 0 && (
        <div class="mb-12">
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
            {buttons.map((button: any) => {
              const buttonClasses = `
                inline-flex items-center justify-center gap-3 px-8 py-4 rounded-xl text-lg font-semibold
                transition-all duration-300 transform hover:scale-105 hover:shadow-2xl
                ${button.variant === 'primary' 
                  ? 'bg-white text-blue-600 hover:bg-gray-100 shadow-xl' 
                  : button.variant === 'secondary'
                  ? 'bg-blue-500 hover:bg-blue-400 text-white shadow-xl'
                  : 'bg-transparent border-2 border-white hover:bg-white hover:text-blue-600 text-white'
                }
              `;
              
              return (
                <a 
                  href={button.url}
                  class={buttonClasses}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={button.text}
                >
                  {button.icon && (
                    <img 
                      src={button.icon} 
                      alt="" 
                      class="w-6 h-6"
                      loading="lazy"
                    />
                  )}
                  <span>{button.text}</span>
                </a>
              );
            })}
          </div>
        </div>
      )}
      
      <!-- App Store Badges -->
      {data.appStoreBadges && data.appStoreBadges.length > 0 && (
        <div class="mb-12">
          <p class="text-blue-100 mb-6 text-lg">Available on:</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            {data.appStoreBadges.map((badge: any) => (
              <a 
                href={badge.url}
                target="_blank"
                rel="noopener noreferrer"
                class="transition-transform duration-300 hover:scale-105"
                aria-label={`Download on ${badge.platform}`}
              >
                <img 
                  src={badge.image} 
                  alt={`Download on ${badge.platform}`}
                  class="h-14 w-auto"
                  loading="lazy"
                />
              </a>
            ))}
          </div>
        </div>
      )}
      
      <!-- Additional Info -->
      <div class="text-center">
        <p class="text-blue-200 text-sm mb-4">
          Free download • No credit card required • Available worldwide
        </p>
        
        <!-- Trust Indicators -->
        <div class="flex flex-wrap justify-center items-center gap-6 text-blue-200 text-sm">
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>Secure & Private</span>
          </div>
          
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Verified App</span>
          </div>
          
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>Award Winning</span>
          </div>
        </div>
      </div>
      
    </div>
  </div>
</section>

<style>
  /* Floating animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-30px); }
  }
  
  @keyframes float-slow {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
    animation-delay: 2s;
  }
  
  .animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
    animation-delay: 4s;
  }
  
  /* Text shadow for better readability */
  .cta-section h2 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .cta-section p {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  /* Button hover effects */
  .cta-section a {
    position: relative;
    overflow: hidden;
  }
  
  .cta-section a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  .cta-section a:hover::before {
    left: 100%;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .cta-section {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .cta-section {
      background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #1e40af 100%);
    }
    
    .cta-section a {
      border-width: 2px;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow {
      animation: none;
    }
    
    .cta-section a {
      transform: none !important;
    }
    
    .cta-section a::before {
      display: none;
    }
  }
  
  /* Print styles */
  @media print {
    .cta-section {
      background: white !important;
      color: black !important;
    }
    
    .cta-section h2,
    .cta-section p {
      color: black !important;
      text-shadow: none !important;
    }
    
    .animate-float,
    .animate-float-delayed,
    .animate-float-slow {
      display: none;
    }
  }
</style>
