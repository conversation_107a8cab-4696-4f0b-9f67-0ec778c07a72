---
/**
 * Footer Component
 * Displays footer links, social media, app store badges, and copyright
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Extract localized content
const links = data.links?.map((link: any) => ({
  ...link,
  text: getLocalizedText(link.text, locale)
})) || [];

const socialMedia = data.socialMedia || [];
const appStoreBadges = data.appStoreBadges || [];

// Get current year for copyright
const currentYear = new Date().getFullYear();
---

<footer class={`footer-section bg-gray-900 text-white ${className}`}>
  <div class="container mx-auto px-4">
    
    <!-- Main Footer Content -->
    <div class="py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        
        <!-- App Info -->
        <div class="lg:col-span-2">
          <div class="mb-6">
            <h3 class="text-2xl font-bold mb-4">Your App Name</h3>
            <p class="text-gray-300 leading-relaxed max-w-md">
              Experience the future of mobile productivity with our innovative app designed to simplify your daily tasks and boost your efficiency.
            </p>
          </div>
          
          <!-- App Store Badges -->
          {appStoreBadges.length > 0 && (
            <div class="mb-6">
              <p class="text-gray-400 mb-4 text-sm">Download now:</p>
              <div class="flex flex-wrap gap-4">
                {appStoreBadges.map((badge: any) => (
                  <a 
                    href={badge.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="transition-transform duration-300 hover:scale-105"
                    aria-label={`Download on ${badge.platform}`}
                  >
                    <img 
                      src={badge.image} 
                      alt={`Download on ${badge.platform}`}
                      class="h-12 w-auto"
                      loading="lazy"
                    />
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
          <ul class="space-y-3">
            {links.map((link: any) => (
              <li>
                <a 
                  href={link.url}
                  class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center gap-2"
                  target={link.external ? "_blank" : "_self"}
                  rel={link.external ? "noopener noreferrer" : ""}
                >
                  <span>{link.text}</span>
                  {link.external && (
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  )}
                </a>
              </li>
            ))}
          </ul>
        </div>
        
        <!-- Contact & Social -->
        <div>
          <h4 class="text-lg font-semibold mb-6">Connect With Us</h4>
          
          <!-- Contact Info -->
          <div class="mb-6 space-y-3">
            <div class="flex items-center gap-3 text-gray-300">
              <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <a href="mailto:<EMAIL>" class="hover:text-white transition-colors duration-200">
                <EMAIL>
              </a>
            </div>
            
            <div class="flex items-center gap-3 text-gray-300">
              <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>San Francisco, CA</span>
            </div>
          </div>
          
          <!-- Social Media -->
          {socialMedia.length > 0 && (
            <div>
              <p class="text-gray-400 mb-4 text-sm">Follow us:</p>
              <div class="flex gap-4">
                {socialMedia.map((social: any) => (
                  <a 
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110"
                    aria-label={`Follow us on ${social.platform}`}
                  >
                    <img 
                      src={social.icon} 
                      alt={social.platform}
                      class="w-5 h-5"
                      loading="lazy"
                    />
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
        
      </div>
    </div>
    
    <!-- Bottom Bar -->
    <div class="border-t border-gray-800 py-8">
      <div class="flex flex-col md:flex-row justify-between items-center gap-4">
        
        <!-- Copyright -->
        <div class="text-gray-400 text-sm">
          <p>&copy; {currentYear} Your App Name. All rights reserved.</p>
        </div>
        
        <!-- Legal Links -->
        <div class="flex flex-wrap gap-6 text-sm">
          <a href="/privacy" class="text-gray-400 hover:text-white transition-colors duration-200">
            Privacy Policy
          </a>
          <a href="/terms" class="text-gray-400 hover:text-white transition-colors duration-200">
            Terms of Service
          </a>
          <a href="/cookies" class="text-gray-400 hover:text-white transition-colors duration-200">
            Cookie Policy
          </a>
        </div>
        
        <!-- Language Selector (if needed) -->
        <div class="flex items-center gap-2 text-sm text-gray-400">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
          </svg>
          <span>English</span>
        </div>
        
      </div>
    </div>
    
  </div>
  
  <!-- Back to Top Button -->
  <button 
    id="back-to-top"
    class="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50"
    aria-label="Back to top"
  >
    <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
    </svg>
  </button>
</footer>

<style>
  /* Footer animations */
  .footer-section {
    position: relative;
  }
  
  .footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  }
  
  /* Back to top button */
  #back-to-top.visible {
    opacity: 1;
    visibility: visible;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .footer-section {
      padding-top: 2rem;
    }
    
    .footer-section .py-16 {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .footer-section {
      border-top: 2px solid #3b82f6;
    }
    
    .footer-section a {
      text-decoration: underline;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .footer-section a,
    #back-to-top {
      transition: none;
      transform: none !important;
    }
  }
  
  /* Print styles */
  @media print {
    .footer-section {
      background: white !important;
      color: black !important;
    }
    
    .footer-section * {
      color: black !important;
    }
    
    #back-to-top {
      display: none;
    }
  }
</style>

<script>
  // Back to top functionality
  document.addEventListener('DOMContentLoaded', function() {
    const backToTopButton = document.getElementById('back-to-top');
    
    if (!backToTopButton) return;
    
    // Show/hide button based on scroll position
    function toggleBackToTop() {
      if (window.scrollY > 300) {
        backToTopButton.classList.add('visible');
      } else {
        backToTopButton.classList.remove('visible');
      }
    }
    
    // Smooth scroll to top
    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
    
    // Event listeners
    window.addEventListener('scroll', toggleBackToTop);
    backToTopButton.addEventListener('click', scrollToTop);
    
    // Initial check
    toggleBackToTop();
  });
</script>
