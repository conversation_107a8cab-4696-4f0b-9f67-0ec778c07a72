---
/**
 * Hero Section Component
 * Renders the main hero section with title, tagline, background, logo, and CTA buttons
 */

import { getLocalizedText } from '../lib/localization';
import type { SupportedLocale } from '../lib/localization';

export interface Props {
  data: any;
  locale?: SupportedLocale;
  className?: string;
}

const { data, locale = 'en', className = '' } = Astro.props;

// Extract localized content
const title = getLocalizedText(data.title, locale);
const tagline = getLocalizedText(data.tagline, locale);
const ctaButtons = data.ctaButtons?.map((button: any) => ({
  ...button,
  text: getLocalizedText(button.text, locale)
})) || [];
---

<section class={`hero-section relative min-h-screen flex items-center justify-center overflow-hidden ${className}`}>
  <!-- Background Image -->
  {data.backgroundImage && (
    <div class="absolute inset-0 z-0">
      <img 
        src={data.backgroundImage} 
        alt="" 
        class="w-full h-full object-cover"
        loading="eager"
      />
      <div class="absolute inset-0 bg-black/40"></div>
    </div>
  )}
  
  <!-- Background Video -->
  {data.videoUrl && (
    <div class="absolute inset-0 z-0">
      <video
        autoplay
        muted
        loop
        playsinline
        class="w-full h-full object-cover"
      >
        <source src={data.videoUrl} type="video/mp4" />
      </video>
      <div class="absolute inset-0 bg-black/40"></div>
    </div>
  )}
  
  <!-- Content -->
  <div class="relative z-10 container mx-auto px-4 text-center text-white">
    <div class="max-w-4xl mx-auto">
      
      <!-- App Logo -->
      {data.appLogo && (
        <div class="mb-8">
          <img 
            src={data.appLogo} 
            alt={title}
            class="w-24 h-24 md:w-32 md:h-32 mx-auto rounded-2xl shadow-2xl"
            loading="eager"
          />
        </div>
      )}
      
      <!-- Title -->
      <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
        {title}
      </h1>
      
      <!-- Tagline -->
      {tagline && (
        <p class="text-xl md:text-2xl lg:text-3xl mb-12 text-gray-200 max-w-3xl mx-auto leading-relaxed">
          {tagline}
        </p>
      )}
      
      <!-- CTA Buttons -->
      {ctaButtons.length > 0 && (
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {ctaButtons.map((button: any) => {
            const buttonClasses = `
              inline-flex items-center justify-center gap-3 px-8 py-4 rounded-xl text-lg font-semibold
              transition-all duration-300 transform hover:scale-105 hover:shadow-xl
              ${button.variant === 'primary' 
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg' 
                : button.variant === 'secondary'
                ? 'bg-white hover:bg-gray-100 text-gray-900 shadow-lg'
                : 'bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white'
              }
            `;
            
            return (
              <a 
                href={button.url}
                class={buttonClasses}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={button.text}
              >
                {button.icon && (
                  <img 
                    src={button.icon} 
                    alt="" 
                    class="w-6 h-6"
                    loading="lazy"
                  />
                )}
                <span>{button.text}</span>
              </a>
            );
          })}
        </div>
      )}
      
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
    <div class="animate-bounce">
      <svg 
        class="w-6 h-6 text-white" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M19 14l-7 7m0 0l-7-7m7 7V3"
        />
      </svg>
    </div>
  </div>
</section>

<style>
  .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  /* Ensure text is readable on all backgrounds */
  .hero-section h1,
  .hero-section p {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .hero-section {
      min-height: 100vh;
      padding-top: 2rem;
      padding-bottom: 2rem;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .hero-section .bg-black\/40 {
      background-color: rgba(0, 0, 0, 0.7);
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-bounce {
      animation: none;
    }
    
    .transform.hover\\:scale-105:hover {
      transform: none;
    }
  }
</style>
