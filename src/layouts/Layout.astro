---
import '../styles/global.css';

export interface Props {
	title: string;
	description?: string;
	keywords?: string;
	author?: string;
	ogImage?: string;
}

const {
	title = "Mobile App Landing Page",
	description = "Mobile app landing page built with Astro and Tailwind CSS",
	keywords = "mobile app, landing page, astro, tailwind",
	author = "Your App Team",
	ogImage = "/images/og-image.jpg"
} = Astro.props;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
	<head>
		<meta charset="UTF-8" />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<meta name="author" content={author} />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="theme-color" content="#3b82f6" />

		<!-- Favicon -->
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="apple-touch-icon" href="/apple-touch-icon.png" />

		<!-- Open Graph Meta Tags -->
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={ogImage} />
		<meta property="og:type" content="website" />
		<meta property="og:url" content={Astro.url} />

		<!-- Twitter Card Meta Tags -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content={title} />
		<meta name="twitter:description" content={description} />
		<meta name="twitter:image" content={ogImage} />

		<!-- Generator -->
		<meta name="generator" content={Astro.generator} />

		<title>{title}</title>
	</head>
	<body class="min-h-screen bg-background font-sans antialiased">
		<!-- Skip link for accessibility -->
		<a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
			Skip to main content
		</a>

		<!-- Main content wrapper -->
		<div id="main-content">
			<slot />
		</div>
	</body>
</html>

<style>
	/* CSS Custom Properties for theming */
	:root {
		--primary: 220 100% 50%;
		--primary-foreground: 0 0% 100%;
		--secondary: 220 14.3% 95.9%;
		--secondary-foreground: 220.9 39.3% 11%;
		--accent: 220 14.3% 95.9%;
		--accent-foreground: 220.9 39.3% 11%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 20% 98%;
		--muted: 220 14.3% 95.9%;
		--muted-foreground: 220 8.9% 46.1%;
		--card: 0 0% 100%;
		--card-foreground: 220.9 39.3% 11%;
		--border: 220 13% 91%;
		--input: 220 13% 91%;
		--ring: 220 100% 50%;
		--background: 0 0% 100%;
		--foreground: 220.9 39.3% 11%;
	}

	/* Dark mode variables */
	@media (prefers-color-scheme: dark) {
		:root {
			--primary: 220 100% 50%;
			--primary-foreground: 220.9 39.3% 11%;
			--secondary: 220.9 39.3% 11%;
			--secondary-foreground: 210 20% 98%;
			--accent: 220.9 39.3% 11%;
			--accent-foreground: 210 20% 98%;
			--destructive: 0 62.8% 30.6%;
			--destructive-foreground: 210 20% 98%;
			--muted: 220.9 39.3% 11%;
			--muted-foreground: 215 20.2% 65.1%;
			--card: 220.9 39.3% 11%;
			--card-foreground: 210 20% 98%;
			--border: 220.9 39.3% 11%;
			--input: 220.9 39.3% 11%;
			--ring: 220 100% 50%;
			--background: 220.9 39.3% 11%;
			--foreground: 210 20% 98%;
		}
	}

	/* Base styles */
	* {
		border-color: hsl(var(--border));
	}

	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
		background-color: hsl(var(--background));
		color: hsl(var(--foreground));
		font-feature-settings: "rlig" 1, "calt" 1;
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		:root {
			--border: 0 0% 0%;
			--ring: 0 0% 0%;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		html {
			scroll-behavior: auto;
		}

		*,
		*::before,
		*::after {
			animation-duration: 0.01ms !important;
			animation-iteration-count: 1 !important;
			transition-duration: 0.01ms !important;
		}
	}
</style>
