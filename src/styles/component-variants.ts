/**
 * Component Styling Variants
 * Centralized styling system for consistent component appearance
 * Based on shadcn design principles with mobile-first responsive design
 */

// Button variants following shadcn design system
export const buttonVariants = {
  base: "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  
  variants: {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    link: "text-primary underline-offset-4 hover:underline",
    primary: "bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl",
    cta: "bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105"
  },
  
  sizes: {
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3",
    lg: "h-11 rounded-md px-8",
    xl: "h-14 rounded-lg px-10 text-lg",
    icon: "h-10 w-10"
  }
};

// Card variants for consistent card styling
export const cardVariants = {
  base: "rounded-lg border bg-card text-card-foreground shadow-sm",
  
  variants: {
    default: "border-border",
    elevated: "shadow-lg hover:shadow-xl transition-shadow duration-300",
    interactive: "hover:shadow-md transition-all duration-300 cursor-pointer hover:-translate-y-1",
    feature: "bg-white border-gray-200 hover:shadow-lg transition-all duration-300 hover:-translate-y-2",
    testimonial: "bg-white border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300"
  },
  
  padding: {
    none: "",
    sm: "p-4",
    default: "p-6",
    lg: "p-8",
    xl: "p-10"
  }
};

// Typography variants for consistent text styling
export const typographyVariants = {
  h1: "scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
  h2: "scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0",
  h3: "scroll-m-20 text-2xl font-semibold tracking-tight",
  h4: "scroll-m-20 text-xl font-semibold tracking-tight",
  p: "leading-7 [&:not(:first-child)]:mt-6",
  lead: "text-xl text-muted-foreground",
  large: "text-lg font-semibold",
  small: "text-sm font-medium leading-none",
  muted: "text-sm text-muted-foreground"
};

// Section variants for consistent section styling
export const sectionVariants = {
  base: "relative",
  
  variants: {
    default: "py-16 md:py-24",
    compact: "py-8 md:py-12",
    hero: "min-h-screen flex items-center justify-center",
    feature: "py-16 md:py-24 bg-gray-50",
    cta: "py-16 md:py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800"
  },
  
  container: {
    default: "container mx-auto px-4",
    narrow: "container mx-auto px-4 max-w-4xl",
    wide: "container mx-auto px-4 max-w-7xl"
  }
};

// Animation variants for consistent animations
export const animationVariants = {
  fadeIn: "opacity-0 animate-[fadeIn_0.6s_ease-out_forwards]",
  slideUp: "opacity-0 translate-y-8 animate-[slideUp_0.6s_ease-out_forwards]",
  slideInLeft: "opacity-0 -translate-x-8 animate-[slideInLeft_0.6s_ease-out_forwards]",
  slideInRight: "opacity-0 translate-x-8 animate-[slideInRight_0.6s_ease-out_forwards]",
  scaleIn: "opacity-0 scale-95 animate-[scaleIn_0.6s_ease-out_forwards]",
  bounce: "animate-bounce",
  pulse: "animate-pulse",
  spin: "animate-spin"
};

// Responsive breakpoint utilities
export const breakpoints = {
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px"
};

// Color palette following shadcn design system
export const colors = {
  primary: {
    50: "#eff6ff",
    100: "#dbeafe",
    500: "#3b82f6",
    600: "#2563eb",
    700: "#1d4ed8",
    900: "#1e3a8a"
  },
  gray: {
    50: "#f9fafb",
    100: "#f3f4f6",
    200: "#e5e7eb",
    300: "#d1d5db",
    400: "#9ca3af",
    500: "#6b7280",
    600: "#4b5563",
    700: "#374151",
    800: "#1f2937",
    900: "#111827"
  }
};

// Accessibility utilities
export const a11yVariants = {
  focusRing: "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
  srOnly: "sr-only",
  skipLink: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50",
  highContrast: "contrast-more:border-2 contrast-more:border-black",
  reducedMotion: "motion-reduce:transition-none motion-reduce:animate-none"
};

// Utility function to combine classes
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

// Utility function to get button classes
export function getButtonClasses(variant: keyof typeof buttonVariants.variants = 'default', size: keyof typeof buttonVariants.sizes = 'default'): string {
  return cn(
    buttonVariants.base,
    buttonVariants.variants[variant],
    buttonVariants.sizes[size],
    a11yVariants.focusRing,
    a11yVariants.reducedMotion
  );
}

// Utility function to get card classes
export function getCardClasses(variant: keyof typeof cardVariants.variants = 'default', padding: keyof typeof cardVariants.padding = 'default'): string {
  return cn(
    cardVariants.base,
    cardVariants.variants[variant],
    cardVariants.padding[padding],
    a11yVariants.highContrast
  );
}

// Utility function to get section classes
export function getSectionClasses(variant: keyof typeof sectionVariants.variants = 'default', container: keyof typeof sectionVariants.container = 'default'): string {
  return cn(
    sectionVariants.base,
    sectionVariants.variants[variant]
  );
}

// Utility function to get container classes
export function getContainerClasses(variant: keyof typeof sectionVariants.container = 'default'): string {
  return sectionVariants.container[variant];
}

// Utility function to get typography classes
export function getTypographyClasses(variant: keyof typeof typographyVariants): string {
  return typographyVariants[variant];
}
