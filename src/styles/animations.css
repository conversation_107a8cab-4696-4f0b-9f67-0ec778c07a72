/**
 * Custom Animations and Responsive Utilities
 * Mobile-first responsive design with accessibility considerations
 */

/* Custom Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-2rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(2rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatDelayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-30px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: floatDelayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
}

/* Staggered Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }

/* Responsive Animation Controls */
@media (max-width: 768px) {
  .animate-on-mobile {
    animation-duration: 0.3s;
  }
  
  .no-animate-mobile {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* Accessibility: Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-bounce,
  .animate-pulse,
  .animate-spin,
  .animate-ping,
  .animate-float,
  .animate-float-delayed {
    animation: none !important;
  }
  
  .hover\\:scale-105:hover,
  .hover\\:scale-110:hover,
  .hover\\:-translate-y-1:hover,
  .hover\\:-translate-y-2:hover {
    transform: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .bg-gradient-to-r,
  .bg-gradient-to-br,
  .bg-gradient-to-l {
    background: #000 !important;
    color: #fff !important;
  }
  
  .text-gray-600,
  .text-gray-500,
  .text-gray-400 {
    color: #000 !important;
  }
  
  .border,
  .border-gray-200,
  .border-gray-300 {
    border-color: #000 !important;
    border-width: 2px !important;
  }
  
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: 0 0 0 2px #000 !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1f2937 !important;
  }
  
  .bg-gray-50 {
    background-color: #111827 !important;
  }
  
  .bg-gray-100 {
    background-color: #1f2937 !important;
  }
  
  .text-gray-900 {
    color: #f9fafb !important;
  }
  
  .text-gray-700 {
    color: #d1d5db !important;
  }
  
  .text-gray-600 {
    color: #9ca3af !important;
  }
  
  .border-gray-200 {
    border-color: #374151 !important;
  }
}

/* Focus Styles for Better Accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-inset {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset;
}

/* Skip Link for Screen Readers */
.skip-link {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50;
}

/* Custom Scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Loading States */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}

/* Interactive Elements */
.interactive-element {
  @apply transition-all duration-300 ease-in-out;
}

.interactive-element:hover {
  @apply transform -translate-y-1 shadow-lg;
}

.interactive-element:active {
  @apply transform translate-y-0 shadow-md;
}

/* Glass Morphism Effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  * {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }
  
  .bg-gradient-to-r,
  .bg-gradient-to-br,
  .bg-gradient-to-l {
    background: #fff !important;
    color: #000 !important;
  }
}
