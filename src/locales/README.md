# Locale Files

This directory contains the UI label translations for the mobile app landing page.

## Available Languages

- `en.json` - English (default)
- `es.json` - Spanish
- `fr.json` - French (example)

## Adding a New Language

To add a new language:

1. Create a new JSON file with the language code (e.g., `de.json` for German)
2. Copy the structure from `en.json`
3. Translate all the values to the target language
4. Update `astro-i18next.config.mjs` to include the new locale in the `locales` array

## Label Structure

Each locale file contains the following UI labels:

```json
{
  "download": "Download Now",
  "learnMore": "Learn More", 
  "getStarted": "Get Started",
  "viewAll": "View All",
  "readMore": "Read More",
  "close": "Close",
  "next": "Next",
  "previous": "Previous",
  "subscribe": "Subscribe",
  "submit": "Submit"
}
```

## Usage

These labels are used throughout the application for UI elements like buttons, links, and form controls. They are separate from the content translations which are stored in the main JSON configuration files.
