{"name": "base_web", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@shadcn/ui": "^0.0.4", "@tailwindcss/vite": "^4.1.8", "astro": "^5.4.3", "astro-i18next": "^1.0.0-beta.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "i18next": "^25.2.1", "lucide-react": "^0.513.0", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "zod": "^3.25.51"}, "devDependencies": {"tw-animate-css": "^1.3.4"}}