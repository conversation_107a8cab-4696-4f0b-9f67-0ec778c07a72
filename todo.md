# TODO: Mobile App Landing Page Development

This TODO list outlines the step-by-step process to develop a mobile app landing page using the Astro framework, shadcn UI components, and a configurable JSON template with localization support. The goal is to create a reusable, user-configurable landing page that drives app downloads and supports multiple languages (e.g., English and Spanish). The JSON template must allow users to easily replace text, asset paths (e.g., images, videos), and URLs without changing the structure, and it must be optimized for Astro’s component-based rendering and shadcn’s accessible, Tailwind-based components. Follow these steps to build the project.

## Step 1: Project Setup
- [x] Initialize a new Astro project.
  - Run `npm create astro@latest` in the terminal.
  - Choose the "Just the basics" starter template and select TypeScript for better type safety.
  - Install dependencies with `npm install`.
- [x] Set up the project structure.
  - Create folders: `src/components`, `src/pages`, `src/data`, `src/styles`, `src/locales`.
  - Create `src/data/landingPage.json` to store the default JSON template.
  - Create `src/data/userConfig.json` for user-customized data.
- [x] Install shadcn UI dependencies.
  - Install Tailwind CSS: `npx astro add tailwind`.
  - Install shadcn CLI: `npm install @shadcn/ui`.
  - Initialize shadcn: `npx shadcn-ui@latest init`.
  - Add required shadcn components (Button, Card, Accordion, Carousel, Testimonial, Badge, Steps, Footer, Link, Form, Pricing Table): `npx shadcn-ui@latest add button card accordion carousel testimonial badge steps footer link form`.
- [x] Configure Tailwind for shadcn.
  - Ensure `tailwind.config.js` includes shadcn’s custom styles.
  - Verify Tailwind is integrated with Astro in `astro.config.mjs`.

## Step 2: Create JSON Template
- [x] Create the JSON template in `src/data/landingPage.json`.
  - Structure it with a root object called `landingPage`.
  - Include the following sections with customizable fields for text, images, URLs, and assets:
    - **Hero**: App name, tagline, background image or video, app logo, CTA buttons (App Store/Google Play links with shadcn Button props like `variant`).
    - **Features**: 3–5 features with titles, descriptions, icons, and optional screenshots (use shadcn Card or List).
    - **How It Works**: 3–4 steps with titles, descriptions, and images (use shadcn Steps or Timeline).
    - **Social Proof**: Testimonials (quotes, author names, avatars, ratings) and metrics (downloads, ratings, optional award image; use shadcn Testimonial and Badge).
    - **Screenshots/Demo**: Screenshots or a demo video (use shadcn Carousel or Video).
    - **CTA**: Title, description, download buttons (use shadcn Button).
    - **About**: Mission or team description, optional team image (use shadcn Card).
    - **FAQ**: 4–6 Q&A pairs (use shadcn Accordion).
    - **Footer**: Links to privacy policy, terms, contact, social media, app store badges (use shadcn Footer and Link).
    - **Optional Sections**: Pricing (with plans, use shadcn Pricing Table) and newsletter signup (use shadcn Form), each with an `enabled` flag.
  - Use placeholder values (e.g., `"path/to/image.jpg"`, `"Your App Name"`) for easy user replacement.
  - Ensure arrays (e.g., `features.items`, `faq.items`) allow adding/removing items.
  - Include optional fields (e.g., `videoUrl`) that can be empty.
  - Add a `meta` object for SEO (title, description, keywords, localized per language).
  - Add a `locale` object for UI labels (e.g., `{ "en": { "download": "Download Now" }, "es": { "download": "Descargar Ahora" } }`).
  - For user content (e.g., `hero.title`), use a localized structure: `{ "en": "App Name", "es": "Nombre de la App" }`.
  - Add comments in the JSON to explain each section/field’s purpose.
- [x] Create a sample user configuration.
  - Duplicate `landingPage.json` as `src/data/userConfig.json`.
  - Fill with sample data (e.g., app name, image paths, URLs) for testing.
- [x] Validate JSON structure.
  - Use a JSON schema validator or linter to ensure correctness.
  - Test with sample data in English and Spanish.

## Step 3: Set Up Localization
- [x] Install a localization library.
  - Run `npm install i18next astro-i18next` for Astro-compatible localization.
  - Configure `astro.config.mjs` to include `astro-i18next`.
- [x] Create locale files for UI labels.
  - In `src/locales`, create `en.json` and `es.json` with labels from `landingPage.locale` (e.g., `{ "download": "Download Now" }`).
  - Ensure easy addition of new languages (e.g., `fr.json`).
- [x] Implement user content localization.
  - Map `landingPage` fields (e.g., `hero.title.en`, `hero.title.es`) to render based on selected language.
- [x] Create a language switcher.
  - Build `src/components/LanguageSwitcher.astro` using shadcn Button or Dropdown.
  - Use `astro-i18next` to toggle between languages (e.g., `en`, `es`) and update UI/content.
- [x] Test localization.
  - Verify UI labels and user content switch correctly.
  - Test edge cases (e.g., missing translations, empty fields).

## Step 4: Develop Astro Components
- [x] Create components for each section in `src/components`.
  - **Hero.astro**: Render `landingPage.hero` with shadcn Button (e.g., `variant="primary"`) and Card for title, tagline, logo, and CTAs.
  - **Features.astro**: Loop through `landingPage.features.items` with shadcn Card or List.
  - **HowItWorks.astro**: Display `landingPage.howItWorks.steps` with shadcn Steps or Timeline.
  - **SocialProof.astro**: Show `landingPage.socialProof.testimonials` with shadcn Testimonial and `metrics` with Badge.
  - **Screenshots.astro**: Render `landingPage.screenshots.images` with shadcn Carousel or `videoUrl` with Video.
  - **CTA.astro**: Display `landingPage.cta` with shadcn Button for download links.
  - **About.astro**: Show `landingPage.about` with shadcn Card.
  - **FAQ.astro**: Render `landingPage.faq.items` with shadcn Accordion.
  - **Footer.astro**: Display `landingPage.footer` with shadcn Footer and Link for links and badges.
  - **Pricing.astro**: Conditionally render `landingPage.optional.pricing` (if `enabled: true`) with shadcn Pricing Table.
  - **Newsletter.astro**: Conditionally render `landingPage.optional.newsletter` (if `enabled: true`) with shadcn Form.
- [x] Map JSON data to components.
  - Use Astro’s `import` to load `landingPage.json` or `userConfig.json`.
  - Pass data as props (e.g., `<Hero data={landingPage.hero} locale={selectedLocale} />`).
- [ ] Style components.
  - Use Tailwind classes for responsive, mobile-first design.
  - Customize shadcn components (e.g., `size`, `variant`) to match app branding.
  - Ensure accessibility (e.g., ARIA labels, keyboard navigation).

## Step 5: Create Landing Page
- [ ] Set up the main page.
  - Create `src/pages/index.astro` as the landing page.
  - Import and render all section components in order: Hero, Features, HowItWorks, SocialProof, Screenshots, CTA, About, FAQ, Footer, and optional Pricing/Newsletter.
  - Use Astro’s `<Fragment>` for conditional rendering of optional sections.
- [ ] Add SEO metadata.
  - Use `landingPage.meta` for dynamic `<title>` and `<meta>` tags, localized per language.
  - Implement Astro’s SEO features (e.g., `Astro.head`).
- [ ] Optimize for responsiveness.
  - Test on mobile, tablet, and desktop using Tailwind’s responsive classes.
  - Use Astro’s `<Image>` component for optimized image loading.

## Step 6: Testing
- [ ] Test component rendering.
  - Verify each section renders correctly with sample data from `userConfig.json`.
  - Check shadcn component functionality (e.g., Accordion toggles, Carousel slides).
- [ ] Test localization.
  - Switch between English and Spanish to confirm UI labels and content update.
  - Test edge cases (e.g., missing translations, empty fields).
- [ ] Test responsiveness.
  - Use browser dev tools to check layout on various screen sizes.
  - Optimize load times with Lighthouse (aim for >90 score).
- [ ] Test accessibility.
  - Run axe or WAVE to ensure WCAG compliance.
  - Verify keyboard navigation and screen reader support.

## Step 7: Documentation
- [ ] Write user instructions in `README.md`.
  - Explain how to:
    - Customize `userConfig.json` (e.g., replace text, asset paths).
    - Upload assets via the config form or file upload.
    - Switch languages using the LanguageSwitcher.
  - Include `sampleConfig.json` and screenshots.
- [ ] Document developer setup.
  - Detail installation (`npm install`), running locally (`npm run dev`), and adding new languages.
  - List shadcn components used and customization options.
- [ ] Provide JSON template example.
  - Include a snippet of `landingPage.json` in `README.md`, showing key fields and localization structure.


## JSON Template Structure (Reference)
```json
{
  "landingPage": {
    "locale": {
      "en": { "download": "Download Now" },
      "es": { "download": "Descargar Ahora" }
    },
    "meta": {
      "en": { "title": "App Title", "description": "App Description" },
      "es": { "title": "Título de la App", "description": "Descripción de la App" }
    },
    "hero": {
      "title": { "en": "App Name", "es": "Nombre de la App" },
      "tagline": { "en": "Tagline", "es": "Eslogan" },
      "backgroundImage": "path/to/image.jpg",
      "appLogo": "path/to/logo.png",
      "videoUrl": "",
      "ctaButtons": [
        { "text": { "en": "Download", "es": "Descargar" }, "url": "link", "icon": "path/to/icon.png", "variant": "primary" }
      ]
    },
    "features": { "title": { "en": "Features", "es": "Características" }, "items": [...] },
    "howItWorks": { "title": { "en": "How It Works", "es": "Cómo Funciona" }, "steps": [...] },
    "socialProof": { "title": { "en": "What Users Say", "es": "Qué Dicen los Usuarios" }, "testimonials": [...], "metrics": {...} },
    "screenshots": { "title": { "en": "See It in Action", "es": "Véalo en Acción" }, "images": [...], "videoUrl": "" },
    "cta": { "title": { "en": "Get Started", "es": "Comenzar" }, "buttons": [...] },
    "about": { "title": { "en": "About Us", "es": "Sobre Nosotros" }, "description": {...} },
    "faq": { "title": { "en": "FAQ", "es": "Preguntas Frecuentes" }, "items": [...] },
    "footer": { "links": [...], "socialMedia": [...], "appStoreBadges": [...] },
    "optional": {
      "pricing": { "enabled": false, "title": {...}, "plans": [...] },
      "newsletter": { "enabled": false, "title": {...}, "formAction": "" }
    }
  }
}