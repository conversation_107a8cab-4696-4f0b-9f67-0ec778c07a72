const fs = require('fs');

console.log('🔍 Starting JSON validation...\n');

try {
  // Read and parse landingPage.json
  console.log('📄 Validating landingPage.json...');
  const landingPageData = fs.readFileSync('src/data/landingPage.json', 'utf8');
  const landingPage = JSON.parse(landingPageData);
  console.log('✅ landingPage.json is valid JSON');

  // Read and parse userConfig.json
  console.log('📄 Validating userConfig.json...');
  const userConfigData = fs.readFileSync('src/data/userConfig.json', 'utf8');
  const userConfig = JSON.parse(userConfigData);
  console.log('✅ userConfig.json is valid JSON');

  // Validate structure
  console.log('\n🏗️  Validating structure...');
  const requiredSections = [
    'locale', 'meta', 'hero', 'features', 'howItWorks', 
    'socialProof', 'screenshots', 'cta', 'about', 'faq', 
    'footer', 'optional'
  ];

  // Check landingPage.json structure
  for (const section of requiredSections) {
    if (!landingPage.landingPage[section]) {
      throw new Error(`Missing section in landingPage.json: ${section}`);
    }
  }
  console.log('✅ All required sections present in landingPage.json');

  // Check userConfig.json structure
  for (const section of requiredSections) {
    if (!userConfig.landingPage[section]) {
      throw new Error(`Missing section in userConfig.json: ${section}`);
    }
  }
  console.log('✅ All required sections present in userConfig.json');

  // Validate localization (English and Spanish)
  console.log('\n🌍 Validating localization...');
  
  // Check locale section
  const landingLocale = landingPage.landingPage.locale;
  if (!landingLocale.en || !landingLocale.es) {
    throw new Error('Missing English or Spanish locale in landingPage.json');
  }
  console.log('✅ Locale section has English and Spanish');

  // Check meta localization
  const landingMeta = landingPage.landingPage.meta;
  if (!landingMeta.en || !landingMeta.es) {
    throw new Error('Missing English or Spanish meta in landingPage.json');
  }
  console.log('✅ Meta section has English and Spanish');

  // Check hero localization
  const landingHero = landingPage.landingPage.hero;
  if (!landingHero.title.en || !landingHero.title.es) {
    throw new Error('Missing English or Spanish hero title in landingPage.json');
  }
  console.log('✅ Hero section has English and Spanish');

  // Check features localization
  const landingFeatures = landingPage.landingPage.features;
  if (!landingFeatures.title.en || !landingFeatures.title.es) {
    throw new Error('Missing English or Spanish features title in landingPage.json');
  }
  console.log('✅ Features section has English and Spanish');

  // Test sample data in userConfig
  console.log('\n📊 Testing sample data...');
  const userHero = userConfig.landingPage.hero;
  if (userHero.title.en && userHero.title.es) {
    console.log(`✅ Sample app name: "${userHero.title.en}" / "${userHero.title.es}"`);
  }

  const userMeta = userConfig.landingPage.meta;
  if (userMeta.en.title && userMeta.es.title) {
    console.log(`✅ Sample meta titles: "${userMeta.en.title}" / "${userMeta.es.title}"`);
  }

  // Check optional sections
  const optionalSections = userConfig.landingPage.optional;
  console.log(`✅ Pricing enabled: ${optionalSections.pricing.enabled}`);
  console.log(`✅ Newsletter enabled: ${optionalSections.newsletter.enabled}`);

  console.log('\n🎉 JSON validation completed successfully!');
  console.log('✅ Both files are syntactically correct');
  console.log('✅ All required sections are present');
  console.log('✅ Localization structure is valid for English and Spanish');
  console.log('✅ Sample data is properly formatted');

} catch (error) {
  console.error('\n❌ Validation failed:', error.message);
  process.exit(1);
}
