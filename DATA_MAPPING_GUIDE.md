# JSON Data Mapping Guide

This guide explains how to map JSON data to Astro components in the mobile app landing page project.

## Overview

The project uses JSON files to store landing page content and maps this data to reusable Astro components. This approach provides flexibility, localization support, and easy content management.

## Data Sources

### 1. User Configuration (`src/data/userConfig.json`)
Contains customized content for a specific app/user:
```json
{
  "landingPage": {
    "meta": {
      "en": { "title": "TaskMaster Pro", "description": "..." },
      "es": { "title": "TaskMaster Pro", "description": "..." }
    },
    "hero": {
      "title": { "en": "TaskMaster Pro", "es": "TaskMaster Pro" },
      "tagline": { "en": "Master your tasks...", "es": "Domina tus tareas..." }
    }
  }
}
```

### 2. Template Data (`src/data/landingPage.json`)
Contains template/default content structure:
```json
{
  "landingPage": {
    "meta": {
      "en": { "title": "Your App Name", "description": "..." },
      "es": { "title": "Nombre de tu App", "description": "..." }
    }
  }
}
```

## Basic Data Mapping Patterns

### 1. Direct Import and Usage

```astro
---
// Import JSON data using Astro's import
import userConfigData from '../data/userConfig.json';
import landingPageData from '../data/landingPage.json';

// Extract the landing page data
const landingPage = userConfigData.landingPage;
---

<!-- Pass data directly to component -->
<Hero 
  data={landingPage.hero} 
  locale="en" 
/>
```

### 2. Dynamic Locale Selection

```astro
---
import userConfigData from '../data/userConfig.json';
import type { SupportedLocale } from '../lib/localization';

// Get locale from URL parameters
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';

const landingPage = userConfigData.landingPage;
---

<Features 
  data={landingPage.features} 
  locale={locale} 
/>
```

### 3. Conditional Rendering

```astro
---
const landingPage = userConfigData.landingPage;
---

<!-- Only render if pricing is enabled -->
{landingPage.optional?.pricing?.enabled && (
  <Pricing 
    data={landingPage.optional.pricing} 
    locale={locale} 
  />
)}

<!-- Only render if newsletter is enabled -->
{landingPage.optional?.newsletter?.enabled && (
  <Newsletter 
    data={landingPage.optional.newsletter} 
    locale={locale} 
  />
)}
```

### 4. Data Source Selection

```astro
---
import userConfigData from '../data/userConfig.json';
import landingPageData from '../data/landingPage.json';

// Select data source based on URL parameter or condition
const url = new URL(Astro.request.url);
const dataSource = url.searchParams.get('data') || 'user';
const landingPage = dataSource === 'template' 
  ? landingPageData.landingPage 
  : userConfigData.landingPage;
---

<Hero data={landingPage.hero} locale={locale} />
```

### 5. Data Transformation

```astro
---
const landingPage = userConfigData.landingPage;

// Transform data before passing to component
const transformedHeroData = {
  ...landingPage.hero,
  // Add custom properties
  customClass: 'special-hero',
  // Modify existing properties
  ctaButtons: landingPage.hero.ctaButtons?.map(button => ({
    ...button,
    variant: 'secondary' // Override button variant
  }))
};
---

<Hero data={transformedHeroData} locale={locale} />
```

### 6. Mixed Data Sources

```astro
---
import userConfigData from '../data/userConfig.json';
import landingPageData from '../data/landingPage.json';

// Mix data from different sources
const mixedData = {
  hero: userConfigData.landingPage.hero,           // User's custom hero
  features: landingPageData.landingPage.features,  // Template features
  howItWorks: userConfigData.landingPage.howItWorks,
  socialProof: landingPageData.landingPage.socialProof
};
---

<Hero data={mixedData.hero} locale={locale} />
<Features data={mixedData.features} locale={locale} />
```

## Component Props Structure

All section components follow a consistent props interface:

```typescript
export interface Props {
  data: any;                    // Section-specific data from JSON
  locale?: SupportedLocale;     // Current language ('en' | 'es')
  className?: string;           // Additional CSS classes
}
```

## Complete Landing Page Example

```astro
---
// src/pages/index.astro
import Layout from '../layouts/Layout.astro';
import LanguageSwitcher from '../components/LanguageSwitcher.astro';

// Import all section components
import Hero from '../components/Hero.astro';
import Features from '../components/Features.astro';
import HowItWorks from '../components/HowItWorks.astro';
import SocialProof from '../components/SocialProof.astro';
import Screenshots from '../components/Screenshots.astro';
import CTA from '../components/CTA.astro';
import About from '../components/About.astro';
import FAQ from '../components/FAQ.astro';
import Footer from '../components/Footer.astro';
import Pricing from '../components/Pricing.astro';
import Newsletter from '../components/Newsletter.astro';

// Import JSON data
import userConfigData from '../data/userConfig.json';
import type { SupportedLocale } from '../lib/localization';

// Get locale from URL
const url = new URL(Astro.request.url);
const localeParam = url.searchParams.get('locale') as SupportedLocale;
const locale: SupportedLocale = (localeParam === 'en' || localeParam === 'es') ? localeParam : 'en';

const landingPage = userConfigData.landingPage;

// Extract meta information for SEO
const metaTitle = landingPage.meta?.[locale]?.title || 'Mobile App Landing Page';
const metaDescription = landingPage.meta?.[locale]?.description || 'Amazing mobile app';
---

<Layout title={metaTitle}>
  <!-- SEO Meta Tags -->
  <meta name="description" content={metaDescription} />
  
  <!-- Language Switcher -->
  <div class="fixed top-4 right-4 z-50">
    <LanguageSwitcher currentLocale={locale} />
  </div>
  
  <main>
    <!-- Map data to each component -->
    <Hero data={landingPage.hero} locale={locale} />
    <Features data={landingPage.features} locale={locale} />
    <HowItWorks data={landingPage.howItWorks} locale={locale} />
    <SocialProof data={landingPage.socialProof} locale={locale} />
    <Screenshots data={landingPage.screenshots} locale={locale} />
    <CTA data={landingPage.cta} locale={locale} />
    <About data={landingPage.about} locale={locale} />
    <FAQ data={landingPage.faq} locale={locale} />
    
    <!-- Optional sections with conditional rendering -->
    {landingPage.optional?.pricing?.enabled && (
      <Pricing data={landingPage.optional.pricing} locale={locale} />
    )}
    
    {landingPage.optional?.newsletter?.enabled && (
      <Newsletter data={landingPage.optional.newsletter} locale={locale} />
    )}
    
    <Footer data={landingPage.footer} locale={locale} />
  </main>
</Layout>
```

## SEO and Meta Data Mapping

```astro
---
const landingPage = userConfigData.landingPage;
const locale = 'en'; // or dynamic

// Extract localized meta information
const metaTitle = landingPage.meta?.[locale]?.title || landingPage.meta?.en?.title || 'Default Title';
const metaDescription = landingPage.meta?.[locale]?.description || landingPage.meta?.en?.description || 'Default Description';
const metaKeywords = landingPage.meta?.[locale]?.keywords || landingPage.meta?.en?.keywords || 'default, keywords';
---

<Layout title={metaTitle}>
  <!-- SEO Meta Tags -->
  <meta name="description" content={metaDescription} />
  <meta name="keywords" content={metaKeywords} />
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content={metaTitle} />
  <meta property="og:description" content={metaDescription} />
  {landingPage.hero?.appLogo && (
    <meta property="og:image" content={landingPage.hero.appLogo} />
  )}
</Layout>
```

## Testing Data Mapping

Visit these pages to see data mapping in action:

1. **Main Landing Page**: `/` - Uses userConfig data
2. **Data Mapping Demo**: `/data-mapping-demo` - Shows different mapping patterns
3. **Localization Test**: `/localization-test-comprehensive` - Tests localization with data mapping

### URL Parameters for Testing

- `?locale=en` or `?locale=es` - Switch languages
- `?data=user` or `?data=template` - Switch data sources (on main page)
- `?source=user` or `?source=template` - Switch data sources (on demo page)

## Best Practices

1. **Always provide fallbacks** for missing data
2. **Use TypeScript types** for better development experience
3. **Extract reusable data transformation logic** into utility functions
4. **Test with both data sources** to ensure compatibility
5. **Validate data structure** before passing to components
6. **Use conditional rendering** for optional sections
7. **Implement proper error handling** for missing or malformed data

## Error Handling

```astro
---
// Safe data access with fallbacks
const heroData = landingPage?.hero || {};
const featuresData = landingPage?.features || { title: { en: 'Features' }, items: [] };

// Validate required fields
if (!heroData.title) {
  console.warn('Hero title is missing');
}
---

<!-- Safe rendering with fallbacks -->
<Hero data={heroData} locale={locale} />
<Features data={featuresData} locale={locale} />
```

This guide provides a comprehensive overview of how to map JSON data to Astro components effectively and safely.
